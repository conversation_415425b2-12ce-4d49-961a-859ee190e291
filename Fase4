¡Perfecto! Voy a proceder con la Fase 4: Adaptar componentes para actualizar los componentes React/Preact existentes para que usen los nuevos servicios y modelos.

<PERSON>ro, voy a analizar los componentes existentes en la carpeta "componentes/" para entender su estructura actual:

Read directory
componentes
Ahora voy a revisar algunos componentes clave para entender cómo están estructurados actualmente y qué necesitan ser adaptados. Empezaré con UserStories:

Read lines 1-50
CreateUserStoryForm.tsx
componentes/UserStories
Veo que este componente ya está usando algunos modelos, pero necesita ser actualizado para usar los nuevos modelos y servicios. Voy a revisar otro componente para entender mejor el patrón:

Read lines 1-50
CreateTaskForm.tsx
componentes/Tasks
Veo que este componente está usando servicios que no existen. Voy a revisar la estructura actual de islands y rutas para entender mejor cómo adaptar los componentes:

Read directory
islands
Read directory
api
routes
Ahora voy a empezar con la adaptación. Primero, voy a crear las rutas API necesarias para las nuevas entidades. Empezaré con User Stories:

Created file
user-stories.ts
routes/api
Ahora voy a crear la ruta API para una user story específica:

Created file
[id].ts
routes/api/user-stories
Ahora voy a crear las rutas API para Tasks:

Created file
tasks.ts
routes/api
Created file
[id].ts
routes/api/tasks
Ahora voy a adaptar el componente CreateUserStoryForm para usar los nuevos modelos y rutas API:

Edited file
CreateUserStoryForm.tsx
componentes/UserStories
Veo que hay varios problemas. Primero, necesito verificar si existen los componentes de formulario y agregar la constante faltante al modelo. Voy a revisar la estructura de componentes:

Read directory
components
Los componentes de formulario no existen. Voy a crear componentes básicos de formulario primero:

Created file
FormField.tsx
components/form
Created file
FormTextarea.tsx
components/form
Created file
FormSelect.tsx
components/form
Created file
FormError.tsx
components/form
Created file
FormActions.tsx
components/form
Ahora voy a agregar la constante faltante al modelo de UserStory:

Edited file
userStory.ts
models
Ahora voy a corregir el componente CreateUserStoryForm:

Edited file
CreateUserStoryForm.tsx
componentes/UserStories
Ahora voy a actualizar las opciones de prioridad para usar la constante:

Edited file
CreateUserStoryForm.tsx
componentes/UserStories
Ahora voy a crear un island para el componente CreateUserStoryForm:

Created file
CreateUserStoryIsland.tsx
islands
Ahora voy a adaptar el componente CreateTaskForm para usar los nuevos modelos y servicios:

Edited file
CreateTaskForm.tsx
componentes/Tasks
Voy a corregir estos errores paso a paso. Primero, voy a actualizar el estado y las funciones:

Edited file
CreateTaskForm.tsx
componentes/Tasks
Voy a reemplazar las llamadas a servicios inexistentes con llamadas a las APIs que creé:

Edited file
CreateTaskForm.tsx
componentes/Tasks
Ahora voy a reemplazar la llamada a createTask con una llamada a la API:

Edited file
CreateTaskForm.tsx
componentes/Tasks
Voy a corregir el error de spentHours y actualizar las opciones de estado:

Edited file
CreateTaskForm.tsx
componentes/Tasks
Voy a corregir las opciones de estado y reemplazar los botones:

Edited file
CreateTaskForm.tsx
componentes/Tasks
Ahora voy a reemplazar los componentes Button con botones HTML normales:

Edited file
CreateTaskForm.tsx
componentes/Tasks
Ahora voy a crear un script de prueba para verificar que las rutas API funcionan correctamente:

Created file
test-api-routes.ts
scripts
Ahora voy a agregar el script de prueba de API a las tareas de deno.json:

Edited file
deno.json
Ahora voy a crear una ruta API para obtener miembros de proyecto que necesita el componente CreateTaskForm:

members.ts
routes/api/projects/[id]
La ruta ya existe. Voy a verificar su contenido:

Read file
members.ts
routes/api/projects/[id]
La ruta existe pero necesita ser actualizada para devolver el formato correcto. Voy a modificarla para que devuelva el formato esperado:

Edited file
members.ts
routes/api/projects/[id]
Ahora voy a crear una página de prueba para probar los componentes adaptados. Primero, voy a crear una página simple para User Stories:

Created file
test-components.tsx
routes
Ahora voy a crear un archivo de documentación para la Fase 4:

terminated
Request ID: 7f5f1aa8-e1ac-4df8-8196-1f657ccc7270