// Enumeración para estados de rúbricas
export enum RubricStatus {
  DRAFT = "draft",
  ACTIVE = "active",
  ARCHIVED = "archived",
}

// Interfaz para niveles de criterios
export interface RubricLevel {
  id: string;
  description: string;
  pointValue: number;
}

// Interfaz para criterios de rúbricas
export interface RubricCriterion {
  id: string;
  name: string;
  description: string;
  maxPoints: number;
  weight?: number; // Peso del criterio (opcional)
  levels: RubricLevel[];
}

// Interfaz para la estructura de criterios en JSON
export interface RubricCriteria {
  criteria: RubricCriterion[];
  totalMaxPoints?: number;
}

// Interfaz para rúbricas
export interface Rubric {
  id: number;
  name: string;
  description: string | null;
  projectId: number | null;
  isTemplate: boolean;
  status: RubricStatus | string;
  criteria: RubricCriteria;
  createdBy: number | null;
  createdAt: Date;
  updatedAt: Date;
}

// Interfaz extendida con datos relacionados
export interface RubricWithDetails extends Rubric {
  project?: {
    id: number;
    name: string;
  };
  creator?: {
    id: number;
    name: string;
    email: string;
  };
  evaluations?: Evaluation[];
  usageCount?: number;
}

// Interfaz para crear una rúbrica
export interface CreateRubricInput {
  name: string;
  description?: string;
  projectId?: number;
  isTemplate?: boolean;
  status?: RubricStatus | string;
  criteria: RubricCriteria;
  createdBy?: number;
}

// Interfaz para actualizar una rúbrica
export interface UpdateRubricInput {
  name?: string;
  description?: string;
  projectId?: number;
  isTemplate?: boolean;
  status?: RubricStatus | string;
  criteria?: RubricCriteria;
}

// Interfaz para duplicar una rúbrica
export interface DuplicateRubricInput {
  name: string;
  projectId?: number;
  isTemplate?: boolean;
}

// Interfaz para filtros de búsqueda
export interface RubricFilters {
  projectId?: number;
  isTemplate?: boolean;
  status?: RubricStatus | string;
  createdBy?: number;
}

// Interfaz para estadísticas de rúbricas
export interface RubricStats {
  total: number;
  templates: number;
  active: number;
  byProject: Record<number, number>;
  averageCriteria: number;
  averageMaxPoints: number;
}

// Tipos auxiliares
export type RubricFormData = Omit<CreateRubricInput, 'createdBy'>;
export type RubricUpdateData = Partial<UpdateRubricInput>;

// Validadores
export function isValidRubricStatus(status: string): status is RubricStatus {
  return Object.values(RubricStatus).includes(status as RubricStatus);
}

export function validateRubricCriteria(criteria: RubricCriteria): string[] {
  const errors: string[] = [];
  
  if (!criteria.criteria || criteria.criteria.length === 0) {
    errors.push("La rúbrica debe tener al menos un criterio");
  }
  
  criteria.criteria.forEach((criterion, index) => {
    if (!criterion.name.trim()) {
      errors.push(`El criterio ${index + 1} debe tener un nombre`);
    }
    
    if (!criterion.description.trim()) {
      errors.push(`El criterio ${index + 1} debe tener una descripción`);
    }
    
    if (criterion.maxPoints <= 0) {
      errors.push(`El criterio ${index + 1} debe tener puntos máximos mayor a 0`);
    }
    
    if (!criterion.levels || criterion.levels.length === 0) {
      errors.push(`El criterio ${index + 1} debe tener al menos un nivel`);
    }
    
    criterion.levels.forEach((level, levelIndex) => {
      if (!level.description.trim()) {
        errors.push(`El nivel ${levelIndex + 1} del criterio ${index + 1} debe tener una descripción`);
      }
      
      if (level.pointValue < 0 || level.pointValue > criterion.maxPoints) {
        errors.push(`El nivel ${levelIndex + 1} del criterio ${index + 1} debe tener puntos entre 0 y ${criterion.maxPoints}`);
      }
    });
  });
  
  return errors;
}

// Funciones de utilidad
export function getRubricStatusColor(status: RubricStatus | string): string {
  switch (status) {
    case RubricStatus.DRAFT:
      return "gray";
    case RubricStatus.ACTIVE:
      return "green";
    case RubricStatus.ARCHIVED:
      return "red";
    default:
      return "gray";
  }
}

export function calculateTotalMaxPoints(criteria: RubricCriteria): number {
  return criteria.criteria.reduce((total, criterion) => total + criterion.maxPoints, 0);
}

export function calculateWeightedMaxPoints(criteria: RubricCriteria): number {
  return criteria.criteria.reduce((total, criterion) => {
    const weight = criterion.weight || 1;
    return total + (criterion.maxPoints * weight);
  }, 0);
}

export function createEmptyRubricCriterion(): RubricCriterion {
  return {
    id: crypto.randomUUID(),
    name: "",
    description: "",
    maxPoints: 10,
    levels: [
      { id: crypto.randomUUID(), description: "Excelente", pointValue: 10 },
      { id: crypto.randomUUID(), description: "Bueno", pointValue: 7 },
      { id: crypto.randomUUID(), description: "Regular", pointValue: 4 },
      { id: crypto.randomUUID(), description: "Insuficiente", pointValue: 1 },
    ],
  };
}

export function createEmptyRubricLevel(): RubricLevel {
  return {
    id: crypto.randomUUID(),
    description: "",
    pointValue: 0,
  };
}

// Constantes
export const RUBRIC_STATUS_OPTIONS = [
  { value: RubricStatus.DRAFT, label: "Borrador", color: "gray" },
  { value: RubricStatus.ACTIVE, label: "Activa", color: "green" },
  { value: RubricStatus.ARCHIVED, label: "Archivada", color: "red" },
];

export const DEFAULT_RUBRIC_LEVELS = [
  { description: "Excelente", pointValue: 10 },
  { description: "Bueno", pointValue: 7 },
  { description: "Regular", pointValue: 4 },
  { description: "Insuficiente", pointValue: 1 },
];

// Importaciones de tipos relacionados
interface Evaluation {
  id: number;
  totalScore: number;
  maxPossibleScore: number;
}
