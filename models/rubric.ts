// Rubric model types and utilities

export interface Rubric {
  id: number;
  name: string;
  description?: string;
  projectId?: number;
  isTemplate: boolean;
  status: RubricStatus;
  criteria: RubricCriterion[];
  createdBy?: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface RubricCriterion {
  id: string;
  name: string;
  description?: string;
  maxPoints: number;
  levels: RubricCriterionLevel[];
}

export interface RubricCriterionLevel {
  id: string;
  description: string;
  pointValue: number;
}

export enum RubricStatus {
  DRAFT = "draft",
  ACTIVE = "active",
  ARCHIVED = "archived"
}

export interface CreateRubricInput {
  name: string;
  description?: string;
  projectId?: number;
  isTemplate?: boolean;
  status?: RubricStatus;
  criteria: RubricCriterion[];
}

export interface UpdateRubricInput {
  name?: string;
  description?: string;
  status?: RubricStatus;
  isTemplate?: boolean;
  criteria?: RubricCriterion[];
}

// Status options for forms
export const RUBRIC_STATUS_OPTIONS = [
  { value: RubricStatus.DRAFT, label: "Borrador" },
  { value: RubricStatus.ACTIVE, label: "Activa" },
  { value: RubricStatus.ARCHIVED, label: "Archivada" },
];

// Utility functions
export function getRubricStatusLabel(status: RubricStatus): string {
  const option = RUBRIC_STATUS_OPTIONS.find(opt => opt.value === status);
  return option?.label || status;
}

export function getRubricStatusColor(status: RubricStatus): string {
  switch (status) {
    case RubricStatus.DRAFT:
      return "text-yellow-600 bg-yellow-100";
    case RubricStatus.ACTIVE:
      return "text-green-600 bg-green-100";
    case RubricStatus.ARCHIVED:
      return "text-gray-600 bg-gray-100";
    default:
      return "text-gray-600 bg-gray-100";
  }
}

// Create rubric with default values
export function createRubricWithDefaults(options: {
  projectId?: number;
  isTemplate?: boolean;
}): Omit<Rubric, "id" | "createdAt" | "updatedAt"> {
  return {
    name: "",
    description: "",
    projectId: options.projectId,
    isTemplate: options.isTemplate || false,
    status: RubricStatus.DRAFT,
    criteria: [],
    createdBy: undefined,
  };
}

// Create default criterion
export function createDefaultCriterion(): RubricCriterion {
  return {
    id: crypto.randomUUID(),
    name: "",
    description: "",
    maxPoints: 10,
    levels: [
      {
        id: crypto.randomUUID(),
        description: "Excelente",
        pointValue: 10,
      },
      {
        id: crypto.randomUUID(),
        description: "Bueno",
        pointValue: 7,
      },
      {
        id: crypto.randomUUID(),
        description: "Regular",
        pointValue: 4,
      },
      {
        id: crypto.randomUUID(),
        description: "Insuficiente",
        pointValue: 1,
      },
    ],
  };
}

// Create default level
export function createDefaultLevel(): RubricCriterionLevel {
  return {
    id: crypto.randomUUID(),
    description: "",
    pointValue: 0,
  };
}

// Validation functions
export function validateRubric(rubric: Partial<CreateRubricInput>): string[] {
  const errors: string[] = [];

  if (!rubric.name?.trim()) {
    errors.push("El nombre es obligatorio");
  }

  if (!rubric.criteria || rubric.criteria.length === 0) {
    errors.push("Debe haber al menos un criterio");
  } else {
    rubric.criteria.forEach((criterion, index) => {
      const criterionErrors = validateCriterion(criterion);
      criterionErrors.forEach(error => {
        errors.push(`Criterio ${index + 1}: ${error}`);
      });
    });
  }

  return errors;
}

export function validateCriterion(criterion: Partial<RubricCriterion>): string[] {
  const errors: string[] = [];

  if (!criterion.name?.trim()) {
    errors.push("El nombre del criterio es obligatorio");
  }

  if (!criterion.maxPoints || criterion.maxPoints <= 0) {
    errors.push("Los puntos máximos deben ser mayores a 0");
  }

  if (!criterion.levels || criterion.levels.length === 0) {
    errors.push("Debe haber al menos un nivel");
  } else {
    criterion.levels.forEach((level, index) => {
      const levelErrors = validateLevel(level);
      levelErrors.forEach(error => {
        errors.push(`Nivel ${index + 1}: ${error}`);
      });
    });
  }

  return errors;
}

export function validateLevel(level: Partial<RubricCriterionLevel>): string[] {
  const errors: string[] = [];

  if (!level.description?.trim()) {
    errors.push("La descripción del nivel es obligatoria");
  }

  if (level.pointValue === undefined || level.pointValue < 0) {
    errors.push("Los puntos deben ser mayores o iguales a 0");
  }

  return errors;
}

// Calculate total points
export function calculateRubricTotalPoints(rubric: Rubric): number {
  return rubric.criteria.reduce((total, criterion) => total + criterion.maxPoints, 0);
}

// Get criterion by ID
export function getCriterionById(rubric: Rubric, criterionId: string): RubricCriterion | undefined {
  return rubric.criteria.find(criterion => criterion.id === criterionId);
}

// Get level by ID within a criterion
export function getLevelById(criterion: RubricCriterion, levelId: string): RubricCriterionLevel | undefined {
  return criterion.levels.find(level => level.id === levelId);
}

// Sort levels by point value (descending)
export function sortLevelsByPoints(levels: RubricCriterionLevel[]): RubricCriterionLevel[] {
  return [...levels].sort((a, b) => b.pointValue - a.pointValue);
}

// Clone rubric for template creation
export function cloneRubricAsTemplate(rubric: Rubric): Omit<Rubric, "id" | "createdAt" | "updatedAt"> {
  return {
    name: `${rubric.name} (Plantilla)`,
    description: rubric.description,
    projectId: undefined, // Templates are not tied to specific projects
    isTemplate: true,
    status: RubricStatus.DRAFT,
    criteria: rubric.criteria.map(criterion => ({
      ...criterion,
      id: crypto.randomUUID(), // Generate new IDs
      levels: criterion.levels.map(level => ({
        ...level,
        id: crypto.randomUUID(), // Generate new IDs
      })),
    })),
    createdBy: undefined,
  };
}

// Rubric with related data
export interface RubricWithDetails extends Rubric {
  project?: {
    id: number;
    name: string;
  };
  creator?: {
    id: number;
    name: string;
  };
  evaluationsCount?: number;
  totalPoints?: number;
}
