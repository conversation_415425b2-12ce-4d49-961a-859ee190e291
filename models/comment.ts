// Comment model and related types

export interface Comment {
  id: string;
  content: string;
  taskId: string;
  userId: string;
  userName: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateCommentInput {
  content: string;
  taskId: string;
  userId: string;
}

export interface UpdateCommentInput {
  content?: string;
}

// Helper functions for comments
export function formatCommentDate(date: Date): string {
  return new Date(date).toLocaleString("es-ES", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
}

export function getCommentInitials(userName: string): string {
  return userName.charAt(0).toUpperCase();
}

export function isCommentOwner(comment: Comment, userId: string): boolean {
  return comment.userId === userId;
}
