// Enumeración para estados de sprints
export enum SprintStatus {
  PLANNED = "planned",
  ACTIVE = "active",
  COMPLETED = "completed",
  CANCELLED = "cancelled",
}

// Interfaz para sprints
export interface Sprint {
  id: number;
  name: string;
  description: string | null;
  goal: string | null;
  projectId: number;
  startDate: Date | null;
  endDate: Date | null;
  status: SprintStatus | string;
  createdBy: number | null;
  createdAt: Date;
  updatedAt: Date;
}

// Interfaz extendida con datos relacionados
export interface SprintWithDetails extends Sprint {
  project?: {
    id: number;
    name: string;
  };
  creator?: {
    id: number;
    name: string;
    email: string;
  };
  userStories?: UserStory[];
  tasks?: Task[];
  deliverables?: Deliverable[];
  stats?: SprintStats;
}

// Interfaz para crear un sprint
export interface CreateSprintInput {
  name: string;
  description?: string;
  goal?: string;
  projectId: number;
  startDate?: Date;
  endDate?: Date;
  status?: SprintStatus | string;
  createdBy?: number;
}

// Interfaz para actualizar un sprint
export interface UpdateSprintInput {
  name?: string;
  description?: string;
  goal?: string;
  startDate?: Date;
  endDate?: Date;
  status?: SprintStatus | string;
}

// Interfaz para filtros de búsqueda
export interface SprintFilters {
  projectId?: number;
  status?: SprintStatus | string;
  createdBy?: number;
  startDateFrom?: Date;
  startDateTo?: Date;
  endDateFrom?: Date;
  endDateTo?: Date;
}

// Interfaz para estadísticas de sprint
export interface SprintStats {
  totalUserStories: number;
  completedUserStories: number;
  totalTasks: number;
  completedTasks: number;
  totalStoryPoints: number;
  completedStoryPoints: number;
  totalEstimatedHours: number;
  totalSpentHours: number;
  burndownData: BurndownPoint[];
  velocity: number;
  completionRate: number;
}

// Interfaz para puntos del burndown chart
export interface BurndownPoint {
  date: Date;
  remainingStoryPoints: number;
  idealRemaining: number;
  completedStoryPoints: number;
}

// Interfaz para métricas de sprint
export interface SprintMetrics {
  sprintId: number;
  sprintName: string;
  duration: number; // días
  plannedStoryPoints: number;
  completedStoryPoints: number;
  velocity: number;
  completionRate: number;
  averageTaskCompletionTime: number;
  teamEfficiency: number;
}

// Tipos auxiliares
export type SprintFormData = Omit<CreateSprintInput, 'projectId' | 'createdBy'>;
export type SprintUpdateData = Partial<UpdateSprintInput>;

// Validadores
export function isValidSprintStatus(status: string): status is SprintStatus {
  return Object.values(SprintStatus).includes(status as SprintStatus);
}

export function validateSprintDates(startDate?: Date, endDate?: Date): string[] {
  const errors: string[] = [];
  
  if (startDate && endDate) {
    if (startDate >= endDate) {
      errors.push("La fecha de inicio debe ser anterior a la fecha de fin");
    }
    
    const duration = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24);
    if (duration > 30) {
      errors.push("La duración del sprint no debe exceder 30 días");
    }
    
    if (duration < 1) {
      errors.push("La duración del sprint debe ser de al menos 1 día");
    }
  }
  
  return errors;
}

// Funciones de utilidad
export function getSprintStatusColor(status: SprintStatus | string): string {
  switch (status) {
    case SprintStatus.PLANNED:
      return "gray";
    case SprintStatus.ACTIVE:
      return "blue";
    case SprintStatus.COMPLETED:
      return "green";
    case SprintStatus.CANCELLED:
      return "red";
    default:
      return "gray";
  }
}

export function getSprintDuration(sprint: Sprint): number | null {
  if (!sprint.startDate || !sprint.endDate) return null;
  const start = new Date(sprint.startDate);
  const end = new Date(sprint.endDate);
  return Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
}

export function getSprintProgress(sprint: Sprint): number {
  if (!sprint.startDate || !sprint.endDate) return 0;
  
  const now = new Date();
  const start = new Date(sprint.startDate);
  const end = new Date(sprint.endDate);
  
  if (now < start) return 0;
  if (now > end) return 100;
  
  const total = end.getTime() - start.getTime();
  const elapsed = now.getTime() - start.getTime();
  
  return Math.round((elapsed / total) * 100);
}

export function isSprintActive(sprint: Sprint): boolean {
  if (sprint.status !== SprintStatus.ACTIVE) return false;
  if (!sprint.startDate || !sprint.endDate) return false;
  
  const now = new Date();
  const start = new Date(sprint.startDate);
  const end = new Date(sprint.endDate);
  
  return now >= start && now <= end;
}

export function isSprintOverdue(sprint: Sprint): boolean {
  if (!sprint.endDate) return false;
  if (sprint.status === SprintStatus.COMPLETED) return false;
  
  return new Date() > new Date(sprint.endDate);
}

export function canStartSprint(sprint: Sprint): boolean {
  return sprint.status === SprintStatus.PLANNED && 
         sprint.startDate && 
         new Date() >= new Date(sprint.startDate);
}

export function canCompleteSprint(sprint: Sprint): boolean {
  return sprint.status === SprintStatus.ACTIVE;
}

export function calculateVelocity(stats: SprintStats): number {
  if (!stats.totalStoryPoints) return 0;
  const duration = getSprintDuration({ 
    startDate: new Date(), 
    endDate: new Date() 
  } as Sprint) || 1;
  return Math.round(stats.completedStoryPoints / (duration / 7)); // Story points per week
}

export function generateBurndownData(
  sprint: Sprint, 
  userStories: any[], 
  tasks: any[]
): BurndownPoint[] {
  if (!sprint.startDate || !sprint.endDate) return [];
  
  const points: BurndownPoint[] = [];
  const start = new Date(sprint.startDate);
  const end = new Date(sprint.endDate);
  const totalDays = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
  const totalStoryPoints = userStories.reduce((sum, story) => sum + (story.points || 0), 0);
  
  for (let day = 0; day <= totalDays; day++) {
    const currentDate = new Date(start.getTime() + day * 24 * 60 * 60 * 1000);
    const idealRemaining = totalStoryPoints * (1 - day / totalDays);
    
    // Calcular puntos completados hasta esta fecha
    const completedStoryPoints = userStories
      .filter(story => story.status === 'done' && new Date(story.updatedAt) <= currentDate)
      .reduce((sum, story) => sum + (story.points || 0), 0);
    
    points.push({
      date: currentDate,
      remainingStoryPoints: totalStoryPoints - completedStoryPoints,
      idealRemaining: Math.max(0, idealRemaining),
      completedStoryPoints,
    });
  }
  
  return points;
}

// Constantes
export const SPRINT_STATUS_OPTIONS = [
  { value: SprintStatus.PLANNED, label: "Planificado", color: "gray" },
  { value: SprintStatus.ACTIVE, label: "Activo", color: "blue" },
  { value: SprintStatus.COMPLETED, label: "Completado", color: "green" },
  { value: SprintStatus.CANCELLED, label: "Cancelado", color: "red" },
];

export const DEFAULT_SPRINT_DURATION = 14; // días
export const MAX_SPRINT_DURATION = 30; // días
export const MIN_SPRINT_DURATION = 1; // día

// Importaciones de tipos relacionados
interface UserStory {
  id: number;
  title: string;
  points: number;
  status: string;
  updatedAt: Date;
}

interface Task {
  id: number;
  title: string;
  status: string;
  storyPoints: number;
  estimatedHours: number;
  spentHours: number;
}

interface Deliverable {
  id: number;
  title: string;
  status: string;
  dueDate: Date;
}
