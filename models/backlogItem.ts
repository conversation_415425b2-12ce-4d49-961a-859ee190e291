// Enumeración para estados de elementos del backlog
export enum BacklogItemStatus {
  BACKLOG = "backlog",
  READY = "ready",
  IN_PROGRESS = "in_progress",
  DONE = "done",
  CANCELLED = "cancelled",
  ON_HOLD = "on_hold",
}

// Enumeración para tipos de elementos del backlog
export enum BacklogItemType {
  FEATURE = "feature",
  BUG = "bug",
  IMPROVEMENT = "improvement",
  TECHNICAL_DEBT = "technical_debt",
  RESEARCH = "research",
  DOCUMENTATION = "documentation",
}

// Interfaz para elementos del backlog
export interface BacklogItem {
  id: number;
  title: string;
  description: string | null;
  projectId: number;
  userStoryId: number | null;
  priority: number; // 0 = más alta prioridad
  status: BacklogItemStatus | string;
  estimatedEffort: number | null; // 1-10 scale
  businessValue: number | null; // 1-10 scale
  createdBy: number | null;
  createdAt: Date;
  updatedAt: Date;
}

// Interfaz extendida con datos relacionados
export interface BacklogItemWithDetails extends BacklogItem {
  project?: {
    id: number;
    name: string;
  };
  userStory?: {
    id: number;
    title: string;
    status: string;
  };
  creator?: {
    id: number;
    name: string;
    email: string;
  };
  tasks?: Task[];
  priorityScore?: number;
}

// Interfaz para crear un elemento del backlog
export interface CreateBacklogItemInput {
  title: string;
  description?: string;
  projectId: number;
  userStoryId?: number;
  priority?: number;
  status?: BacklogItemStatus | string;
  estimatedEffort?: number;
  businessValue?: number;
  createdBy?: number;
}

// Interfaz para actualizar un elemento del backlog
export interface UpdateBacklogItemInput {
  title?: string;
  description?: string;
  userStoryId?: number;
  priority?: number;
  status?: BacklogItemStatus | string;
  estimatedEffort?: number;
  businessValue?: number;
}

// Interfaz para filtros de búsqueda
export interface BacklogItemFilters {
  projectId?: number;
  userStoryId?: number;
  status?: BacklogItemStatus | string;
  createdBy?: number;
  minPriority?: number;
  maxPriority?: number;
  minEffort?: number;
  maxEffort?: number;
  minBusinessValue?: number;
  maxBusinessValue?: number;
}

// Interfaz para estadísticas del backlog
export interface BacklogStats {
  total: number;
  byStatus: Record<BacklogItemStatus, number>;
  averagePriority: number;
  averageEffort: number;
  averageBusinessValue: number;
  totalEstimatedEffort: number;
  highPriorityItems: number;
  readyItems: number;
}

// Interfaz para priorización del backlog
export interface BacklogPrioritization {
  itemId: number;
  title: string;
  priority: number;
  estimatedEffort: number;
  businessValue: number;
  priorityScore: number;
  roi: number; // Return on Investment
  rank: number;
}

// Tipos auxiliares
export type BacklogItemFormData = Omit<CreateBacklogItemInput, 'projectId' | 'createdBy'>;
export type BacklogItemUpdateData = Partial<UpdateBacklogItemInput>;

// Validadores
export function isValidBacklogItemStatus(status: string): status is BacklogItemStatus {
  return Object.values(BacklogItemStatus).includes(status as BacklogItemStatus);
}

export function isValidBacklogItemType(type: string): type is BacklogItemType {
  return Object.values(BacklogItemType).includes(type as BacklogItemType);
}

export function validateEffortValue(effort: number): boolean {
  return effort >= 1 && effort <= 10;
}

export function validateBusinessValue(value: number): boolean {
  return value >= 1 && value <= 10;
}

export function validatePriority(priority: number): boolean {
  return priority >= 0 && Number.isInteger(priority);
}

// Funciones de utilidad
export function getBacklogItemStatusColor(status: BacklogItemStatus | string): string {
  switch (status) {
    case BacklogItemStatus.BACKLOG:
      return "gray";
    case BacklogItemStatus.READY:
      return "blue";
    case BacklogItemStatus.IN_PROGRESS:
      return "yellow";
    case BacklogItemStatus.DONE:
      return "green";
    case BacklogItemStatus.CANCELLED:
      return "red";
    case BacklogItemStatus.ON_HOLD:
      return "orange";
    default:
      return "gray";
  }
}

export function getBacklogItemTypeIcon(type: BacklogItemType | string): string {
  switch (type) {
    case BacklogItemType.FEATURE:
      return "✨";
    case BacklogItemType.BUG:
      return "🐛";
    case BacklogItemType.IMPROVEMENT:
      return "🔧";
    case BacklogItemType.TECHNICAL_DEBT:
      return "⚠️";
    case BacklogItemType.RESEARCH:
      return "🔍";
    case BacklogItemType.DOCUMENTATION:
      return "📚";
    default:
      return "📋";
  }
}

export function calculatePriorityScore(item: BacklogItem): number {
  const effort = item.estimatedEffort || 5;
  const businessValue = item.businessValue || 5;
  
  // Fórmula: (Business Value / Effort) * 100
  // Valores más altos = mayor prioridad
  return Math.round((businessValue / effort) * 100);
}

export function calculateROI(item: BacklogItem): number {
  const effort = item.estimatedEffort || 5;
  const businessValue = item.businessValue || 5;
  
  // ROI = (Business Value - Effort) / Effort * 100
  return Math.round(((businessValue - effort) / effort) * 100);
}

export function getPriorityLabel(priority: number): string {
  if (priority === 0) return "Crítica";
  if (priority <= 5) return "Alta";
  if (priority <= 10) return "Media";
  if (priority <= 20) return "Baja";
  return "Muy Baja";
}

export function getPriorityColor(priority: number): string {
  if (priority === 0) return "red";
  if (priority <= 5) return "orange";
  if (priority <= 10) return "yellow";
  if (priority <= 20) return "blue";
  return "gray";
}

export function canMoveToReady(item: BacklogItem): boolean {
  return item.status === BacklogItemStatus.BACKLOG && 
         item.estimatedEffort !== null && 
         item.businessValue !== null;
}

export function canStartWork(item: BacklogItem): boolean {
  return item.status === BacklogItemStatus.READY;
}

export function sortBacklogByPriority(items: BacklogItem[]): BacklogItem[] {
  return [...items].sort((a, b) => {
    // Primero por prioridad (menor número = mayor prioridad)
    if (a.priority !== b.priority) {
      return a.priority - b.priority;
    }
    
    // Luego por score de prioridad
    const scoreA = calculatePriorityScore(a);
    const scoreB = calculatePriorityScore(b);
    return scoreB - scoreA;
  });
}

export function groupBacklogByStatus(items: BacklogItem[]): Record<BacklogItemStatus, BacklogItem[]> {
  const grouped: Record<BacklogItemStatus, BacklogItem[]> = {
    [BacklogItemStatus.BACKLOG]: [],
    [BacklogItemStatus.READY]: [],
    [BacklogItemStatus.IN_PROGRESS]: [],
    [BacklogItemStatus.DONE]: [],
    [BacklogItemStatus.CANCELLED]: [],
    [BacklogItemStatus.ON_HOLD]: [],
  };
  
  items.forEach(item => {
    const status = item.status as BacklogItemStatus;
    if (grouped[status]) {
      grouped[status].push(item);
    }
  });
  
  return grouped;
}

export function reorderBacklogPriorities(items: BacklogItem[]): BacklogItem[] {
  return items.map((item, index) => ({
    ...item,
    priority: index,
  }));
}

// Constantes
export const BACKLOG_ITEM_STATUS_OPTIONS = [
  { value: BacklogItemStatus.BACKLOG, label: "Backlog", color: "gray" },
  { value: BacklogItemStatus.READY, label: "Listo", color: "blue" },
  { value: BacklogItemStatus.IN_PROGRESS, label: "En progreso", color: "yellow" },
  { value: BacklogItemStatus.DONE, label: "Completado", color: "green" },
  { value: BacklogItemStatus.CANCELLED, label: "Cancelado", color: "red" },
  { value: BacklogItemStatus.ON_HOLD, label: "En espera", color: "orange" },
];

export const BACKLOG_ITEM_TYPE_OPTIONS = [
  { value: BacklogItemType.FEATURE, label: "Funcionalidad", icon: "✨", color: "blue" },
  { value: BacklogItemType.BUG, label: "Error", icon: "🐛", color: "red" },
  { value: BacklogItemType.IMPROVEMENT, label: "Mejora", icon: "🔧", color: "green" },
  { value: BacklogItemType.TECHNICAL_DEBT, label: "Deuda Técnica", icon: "⚠️", color: "orange" },
  { value: BacklogItemType.RESEARCH, label: "Investigación", icon: "🔍", color: "purple" },
  { value: BacklogItemType.DOCUMENTATION, label: "Documentación", icon: "📚", color: "gray" },
];

export const EFFORT_SCALE = [
  { value: 1, label: "Muy Fácil", description: "< 1 hora" },
  { value: 2, label: "Fácil", description: "1-2 horas" },
  { value: 3, label: "Moderado", description: "2-4 horas" },
  { value: 5, label: "Medio", description: "4-8 horas" },
  { value: 8, label: "Difícil", description: "1-2 días" },
  { value: 10, label: "Muy Difícil", description: "> 2 días" },
];

export const BUSINESS_VALUE_SCALE = [
  { value: 1, label: "Muy Bajo", description: "Impacto mínimo" },
  { value: 3, label: "Bajo", description: "Impacto menor" },
  { value: 5, label: "Medio", description: "Impacto moderado" },
  { value: 8, label: "Alto", description: "Impacto significativo" },
  { value: 10, label: "Muy Alto", description: "Impacto crítico" },
];

// Importaciones de tipos relacionados
interface Task {
  id: number;
  title: string;
  status: string;
}
