// Task model and related types

export enum TaskStatus {
  TODO = "todo",
  IN_PROGRESS = "in_progress", 
  REVIEW = "review",
  DONE = "done",
  BLOCKED = "blocked"
}

export enum TaskPriority {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
  URGENT = "urgent"
}

export interface Task {
  id: number;
  title: string;
  description?: string;
  status: TaskStatus;
  priority: TaskPriority;
  storyPoints?: number;
  estimatedHours?: number;
  spentHours?: number;
  userStoryId?: number;
  sprintId?: number;
  assigneeId?: number;
  assignedTo?: string;
  createdBy?: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateTaskInput {
  title: string;
  description?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  storyPoints?: number;
  estimatedHours?: number;
  spentHours?: number;
  userStoryId?: number;
  sprintId?: number;
  assigneeId?: number;
  assignedTo?: string;
  createdBy?: number;
}

export interface UpdateTaskInput {
  title?: string;
  description?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  storyPoints?: number;
  estimatedHours?: number;
  spentHours?: number;
  userStoryId?: number;
  sprintId?: number;
  assigneeId?: number;
  assignedTo?: string;
}

// Task status options for UI
export const TASK_STATUS_OPTIONS = [
  { value: TaskStatus.TODO, label: "Por hacer" },
  { value: TaskStatus.IN_PROGRESS, label: "En progreso" },
  { value: TaskStatus.REVIEW, label: "En revisión" },
  { value: TaskStatus.DONE, label: "Completado" },
  { value: TaskStatus.BLOCKED, label: "Bloqueado" }
];

// Task priority options for UI
export const TASK_PRIORITY_OPTIONS = [
  { value: TaskPriority.LOW, label: "Baja" },
  { value: TaskPriority.MEDIUM, label: "Media" },
  { value: TaskPriority.HIGH, label: "Alta" },
  { value: TaskPriority.URGENT, label: "Urgente" }
];

// Helper functions
export function getTaskStatusLabel(status: TaskStatus): string {
  const option = TASK_STATUS_OPTIONS.find(opt => opt.value === status);
  return option?.label || status;
}

export function getTaskPriorityLabel(priority: TaskPriority): string {
  const option = TASK_PRIORITY_OPTIONS.find(opt => opt.value === priority);
  return option?.label || priority;
}

export function getTaskStatusColor(status: TaskStatus): string {
  switch (status) {
    case TaskStatus.TODO:
      return "bg-gray-100 text-gray-800";
    case TaskStatus.IN_PROGRESS:
      return "bg-blue-100 text-blue-800";
    case TaskStatus.REVIEW:
      return "bg-yellow-100 text-yellow-800";
    case TaskStatus.DONE:
      return "bg-green-100 text-green-800";
    case TaskStatus.BLOCKED:
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
}

export function getTaskPriorityColor(priority: TaskPriority): string {
  switch (priority) {
    case TaskPriority.LOW:
      return "bg-green-100 text-green-800";
    case TaskPriority.MEDIUM:
      return "bg-yellow-100 text-yellow-800";
    case TaskPriority.HIGH:
      return "bg-orange-100 text-orange-800";
    case TaskPriority.URGENT:
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
}

// Calculate task progress based on hours
export function calculateTaskProgress(estimatedHours?: number, spentHours?: number): number {
  if (!estimatedHours || estimatedHours === 0) return 0;
  if (!spentHours) return 0;
  
  const progress = (spentHours / estimatedHours) * 100;
  return Math.min(progress, 100); // Cap at 100%
}

// Check if task is overdue (for future use with due dates)
export function isTaskOverdue(dueDate?: Date): boolean {
  if (!dueDate) return false;
  return new Date() > dueDate;
}

// Get task completion percentage based on status
export function getTaskCompletionPercentage(status: TaskStatus): number {
  switch (status) {
    case TaskStatus.TODO:
      return 0;
    case TaskStatus.IN_PROGRESS:
      return 50;
    case TaskStatus.REVIEW:
      return 80;
    case TaskStatus.DONE:
      return 100;
    case TaskStatus.BLOCKED:
      return 0;
    default:
      return 0;
  }
}
