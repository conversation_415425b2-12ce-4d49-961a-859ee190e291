// Enumeración para estados de tareas
export enum TaskStatus {
  TODO = "todo",
  IN_PROGRESS = "in_progress",
  IN_REVIEW = "in_review",
  DONE = "done",
  BLOCKED = "blocked",
  CANCELLED = "cancelled",
}

// Enumeración para prioridades de tareas
export enum TaskPriority {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
  URGENT = "urgent",
}

// Interfaz para tareas
export interface Task {
  id: number;
  title: string;
  description: string | null;
  userStoryId: number | null;
  sprintId: number | null;
  assigneeId: number | null;
  assignedTo: string | null;
  status: TaskStatus | string;
  priority: TaskPriority | string;
  storyPoints: number | null;
  estimatedHours: number | null;
  spentHours: number | null;
  createdBy: number | null;
  createdAt: Date;
  updatedAt: Date;
}

// Interfaz extendida con datos relacionados
export interface TaskWithDetails extends Task {
  userStory?: {
    id: number;
    title: string;
    priority: string;
  };
  sprint?: {
    id: number;
    name: string;
  };
  assignee?: {
    id: number;
    name: string;
    email: string;
    username?: string;
  };
  creator?: {
    id: number;
    name: string;
    email: string;
  };
}

// Interfaz para crear una tarea
export interface CreateTaskInput {
  title: string;
  description?: string;
  userStoryId?: number;
  sprintId?: number;
  assigneeId?: number;
  assignedTo?: string;
  status?: TaskStatus | string;
  priority?: TaskPriority | string;
  storyPoints?: number;
  estimatedHours?: number;
  createdBy?: number;
}

// Interfaz para actualizar una tarea
export interface UpdateTaskInput {
  title?: string;
  description?: string;
  userStoryId?: number;
  sprintId?: number;
  assigneeId?: number;
  assignedTo?: string;
  status?: TaskStatus | string;
  priority?: TaskPriority | string;
  storyPoints?: number;
  estimatedHours?: number;
  spentHours?: number;
}

// Interfaz para filtros de búsqueda
export interface TaskFilters {
  userStoryId?: number;
  sprintId?: number;
  assigneeId?: number;
  status?: TaskStatus | string;
  priority?: TaskPriority | string;
  createdBy?: number;
}

// Interfaz para estadísticas de tareas
export interface TaskStats {
  total: number;
  byStatus: Record<TaskStatus, number>;
  byPriority: Record<TaskPriority, number>;
  totalStoryPoints: number;
  completedStoryPoints: number;
  totalEstimatedHours: number;
  totalSpentHours: number;
  averageCompletionTime: number;
}

// Interfaz para tiempo de trabajo
export interface TaskTimeEntry {
  taskId: number;
  userId: number;
  hours: number;
  description?: string;
  date: Date;
}

// Tipos auxiliares
export type TaskFormData = Omit<CreateTaskInput, 'createdBy'>;
export type TaskUpdateData = Partial<UpdateTaskInput>;

// Validadores
export function isValidTaskStatus(status: string): status is TaskStatus {
  return Object.values(TaskStatus).includes(status as TaskStatus);
}

export function isValidTaskPriority(priority: string): priority is TaskPriority {
  return Object.values(TaskPriority).includes(priority as TaskPriority);
}

// Funciones de utilidad
export function getTaskStatusColor(status: TaskStatus | string): string {
  switch (status) {
    case TaskStatus.TODO:
      return "gray";
    case TaskStatus.IN_PROGRESS:
      return "blue";
    case TaskStatus.IN_REVIEW:
      return "yellow";
    case TaskStatus.DONE:
      return "green";
    case TaskStatus.BLOCKED:
      return "red";
    case TaskStatus.CANCELLED:
      return "gray";
    default:
      return "gray";
  }
}

export function getTaskPriorityColor(priority: TaskPriority | string): string {
  switch (priority) {
    case TaskPriority.URGENT:
      return "red";
    case TaskPriority.HIGH:
      return "orange";
    case TaskPriority.MEDIUM:
      return "yellow";
    case TaskPriority.LOW:
      return "green";
    default:
      return "yellow";
  }
}

export function getTaskPriorityWeight(priority: TaskPriority | string): number {
  switch (priority) {
    case TaskPriority.URGENT:
      return 4;
    case TaskPriority.HIGH:
      return 3;
    case TaskPriority.MEDIUM:
      return 2;
    case TaskPriority.LOW:
      return 1;
    default:
      return 2;
  }
}

export function calculateTaskProgress(task: Task): number {
  if (task.status === TaskStatus.DONE) return 100;
  if (task.status === TaskStatus.IN_REVIEW) return 90;
  if (task.status === TaskStatus.IN_PROGRESS) return 50;
  if (task.status === TaskStatus.TODO) return 0;
  if (task.status === TaskStatus.BLOCKED) return 25;
  if (task.status === TaskStatus.CANCELLED) return 0;
  return 0;
}

export function isTaskOverdue(task: Task, dueDate?: Date): boolean {
  if (!dueDate) return false;
  return new Date() > dueDate && task.status !== TaskStatus.DONE;
}

export function getTaskEfficiency(task: Task): number | null {
  if (!task.estimatedHours || !task.spentHours) return null;
  return (task.estimatedHours / task.spentHours) * 100;
}

// Constantes
export const TASK_STATUS_OPTIONS = [
  { value: TaskStatus.TODO, label: "Por hacer", color: "gray" },
  { value: TaskStatus.IN_PROGRESS, label: "En progreso", color: "blue" },
  { value: TaskStatus.IN_REVIEW, label: "En revisión", color: "yellow" },
  { value: TaskStatus.DONE, label: "Completado", color: "green" },
  { value: TaskStatus.BLOCKED, label: "Bloqueado", color: "red" },
  { value: TaskStatus.CANCELLED, label: "Cancelado", color: "gray" },
];

export const TASK_PRIORITY_OPTIONS = [
  { value: TaskPriority.LOW, label: "Baja", color: "green" },
  { value: TaskPriority.MEDIUM, label: "Media", color: "yellow" },
  { value: TaskPriority.HIGH, label: "Alta", color: "orange" },
  { value: TaskPriority.URGENT, label: "Urgente", color: "red" },
];
