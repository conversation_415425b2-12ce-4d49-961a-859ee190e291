// Evaluation model types and utilities

export interface Evaluation {
  id: number;
  deliverableId?: number;
  studentId: number;
  evaluatorId: number;
  rubricId: number;
  criteriaEvaluations: CriterionEvaluation[];
  overallFeedback?: string;
  totalScore: number;
  maxPossibleScore: number;
  percentage?: number;
  status: EvaluationStatus;
  type: EvaluationType;
  createdAt: Date;
  updatedAt: Date;
}

export interface CriterionEvaluation {
  criterionId: string;
  score: number;
  feedback?: string;
}

export enum EvaluationStatus {
  DRAFT = "draft",
  IN_PROGRESS = "in_progress",
  COMPLETED = "completed",
  REVIEWED = "reviewed",
  PUBLISHED = "published"
}

export enum EvaluationType {
  DELIVERABLE = "deliverable",
  PEER = "peer",
  SELF = "self",
  PRESENTATION = "presentation",
  PARTICIPATION = "participation",
  OTHER = "other"
}

export interface CreateEvaluationInput {
  deliverableId?: number;
  studentId: number;
  rubricId: number;
  criteriaEvaluations: CriterionEvaluation[];
  overallFeedback?: string;
  totalScore: number;
  maxPossibleScore: number;
  status?: EvaluationStatus;
  type?: EvaluationType;
}

export interface UpdateEvaluationInput {
  criteriaEvaluations?: CriterionEvaluation[];
  overallFeedback?: string;
  totalScore?: number;
  maxPossibleScore?: number;
  status?: EvaluationStatus;
}

// Status options for forms
export const EVALUATION_STATUS_OPTIONS = [
  { value: EvaluationStatus.DRAFT, label: "Borrador" },
  { value: EvaluationStatus.IN_PROGRESS, label: "En Progreso" },
  { value: EvaluationStatus.COMPLETED, label: "Completada" },
  { value: EvaluationStatus.REVIEWED, label: "Revisada" },
  { value: EvaluationStatus.PUBLISHED, label: "Publicada" },
];

// Type options for forms
export const EVALUATION_TYPE_OPTIONS = [
  { value: EvaluationType.DELIVERABLE, label: "Entregable" },
  { value: EvaluationType.PEER, label: "Evaluación de Pares" },
  { value: EvaluationType.SELF, label: "Autoevaluación" },
  { value: EvaluationType.PRESENTATION, label: "Presentación" },
  { value: EvaluationType.PARTICIPATION, label: "Participación" },
  { value: EvaluationType.OTHER, label: "Otro" },
];

// Utility functions
export function getEvaluationStatusLabel(status: EvaluationStatus): string {
  const option = EVALUATION_STATUS_OPTIONS.find(opt => opt.value === status);
  return option?.label || status;
}

export function getEvaluationTypeLabel(type: EvaluationType): string {
  const option = EVALUATION_TYPE_OPTIONS.find(opt => opt.value === type);
  return option?.label || type;
}

export function getEvaluationStatusColor(status: EvaluationStatus): string {
  switch (status) {
    case EvaluationStatus.DRAFT:
      return "text-gray-600 bg-gray-100";
    case EvaluationStatus.IN_PROGRESS:
      return "text-yellow-600 bg-yellow-100";
    case EvaluationStatus.COMPLETED:
      return "text-blue-600 bg-blue-100";
    case EvaluationStatus.REVIEWED:
      return "text-purple-600 bg-purple-100";
    case EvaluationStatus.PUBLISHED:
      return "text-green-600 bg-green-100";
    default:
      return "text-gray-600 bg-gray-100";
  }
}

export function getEvaluationTypeColor(type: EvaluationType): string {
  switch (type) {
    case EvaluationType.DELIVERABLE:
      return "text-blue-600 bg-blue-100";
    case EvaluationType.PEER:
      return "text-green-600 bg-green-100";
    case EvaluationType.SELF:
      return "text-purple-600 bg-purple-100";
    case EvaluationType.PRESENTATION:
      return "text-orange-600 bg-orange-100";
    case EvaluationType.PARTICIPATION:
      return "text-indigo-600 bg-indigo-100";
    case EvaluationType.OTHER:
      return "text-gray-600 bg-gray-100";
    default:
      return "text-gray-600 bg-gray-100";
  }
}

// Calculation functions
export function calculateEvaluationPercentage(evaluation: Evaluation): number {
  if (evaluation.maxPossibleScore === 0) return 0;
  return Math.round((evaluation.totalScore / evaluation.maxPossibleScore) * 100);
}

export function calculateTotalScore(criteriaEvaluations: CriterionEvaluation[]): number {
  return criteriaEvaluations.reduce((total, criterion) => total + criterion.score, 0);
}

export function getLetterGrade(percentage: number): string {
  if (percentage >= 90) return "A";
  if (percentage >= 80) return "B";
  if (percentage >= 70) return "C";
  if (percentage >= 60) return "D";
  return "F";
}

export function getGradeColor(percentage: number): string {
  if (percentage >= 90) return "text-green-600 bg-green-100";
  if (percentage >= 80) return "text-blue-600 bg-blue-100";
  if (percentage >= 70) return "text-yellow-600 bg-yellow-100";
  if (percentage >= 60) return "text-orange-600 bg-orange-100";
  return "text-red-600 bg-red-100";
}

// Validation functions
export function validateEvaluation(evaluation: Partial<CreateEvaluationInput>): string[] {
  const errors: string[] = [];

  if (!evaluation.studentId) {
    errors.push("El estudiante es obligatorio");
  }

  if (!evaluation.rubricId) {
    errors.push("La rúbrica es obligatoria");
  }

  if (!evaluation.criteriaEvaluations || evaluation.criteriaEvaluations.length === 0) {
    errors.push("Debe evaluar al menos un criterio");
  }

  if (evaluation.totalScore !== undefined && evaluation.totalScore < 0) {
    errors.push("La puntuación total no puede ser negativa");
  }

  if (evaluation.maxPossibleScore !== undefined && evaluation.maxPossibleScore <= 0) {
    errors.push("La puntuación máxima debe ser mayor a 0");
  }

  if (evaluation.totalScore !== undefined && 
      evaluation.maxPossibleScore !== undefined && 
      evaluation.totalScore > evaluation.maxPossibleScore) {
    errors.push("La puntuación total no puede exceder la puntuación máxima");
  }

  return errors;
}

export function validateCriterionEvaluation(criterionEvaluation: Partial<CriterionEvaluation>): string[] {
  const errors: string[] = [];

  if (!criterionEvaluation.criterionId) {
    errors.push("El ID del criterio es obligatorio");
  }

  if (criterionEvaluation.score === undefined || criterionEvaluation.score < 0) {
    errors.push("La puntuación debe ser mayor o igual a 0");
  }

  return errors;
}

// Utility functions for criterion evaluations
export function getCriterionEvaluationById(
  evaluation: Evaluation, 
  criterionId: string
): CriterionEvaluation | undefined {
  return evaluation.criteriaEvaluations.find(ce => ce.criterionId === criterionId);
}

export function updateCriterionEvaluation(
  evaluation: Evaluation,
  criterionId: string,
  updates: Partial<CriterionEvaluation>
): Evaluation {
  const updatedCriteriaEvaluations = evaluation.criteriaEvaluations.map(ce => 
    ce.criterionId === criterionId ? { ...ce, ...updates } : ce
  );

  const totalScore = calculateTotalScore(updatedCriteriaEvaluations);
  const percentage = calculateEvaluationPercentage({
    ...evaluation,
    totalScore,
    criteriaEvaluations: updatedCriteriaEvaluations,
  });

  return {
    ...evaluation,
    criteriaEvaluations: updatedCriteriaEvaluations,
    totalScore,
    percentage,
  };
}

// Statistics functions
export function calculateEvaluationStats(evaluations: Evaluation[]) {
  if (evaluations.length === 0) {
    return {
      count: 0,
      averageScore: 0,
      averagePercentage: 0,
      highestScore: 0,
      lowestScore: 0,
      passRate: 0,
    };
  }

  const scores = evaluations.map(e => e.totalScore);
  const percentages = evaluations.map(e => calculateEvaluationPercentage(e));
  
  return {
    count: evaluations.length,
    averageScore: scores.reduce((sum, score) => sum + score, 0) / scores.length,
    averagePercentage: percentages.reduce((sum, pct) => sum + pct, 0) / percentages.length,
    highestScore: Math.max(...scores),
    lowestScore: Math.min(...scores),
    passRate: (percentages.filter(pct => pct >= 60).length / percentages.length) * 100,
  };
}

// Evaluation with related data
export interface EvaluationWithDetails extends Evaluation {
  deliverable?: {
    id: number;
    title: string;
    type: string;
  };
  student?: {
    id: number;
    name: string;
    email: string;
  };
  evaluator?: {
    id: number;
    name: string;
  };
  rubric?: {
    id: number;
    name: string;
    criteria: any[];
  };
}
