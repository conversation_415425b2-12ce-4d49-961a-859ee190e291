// Enumeración para estados de evaluaciones
export enum EvaluationStatus {
  DRAFT = "draft",
  IN_PROGRESS = "in_progress",
  COMPLETED = "completed",
  REVIEWED = "reviewed",
  PUBLISHED = "published",
}

// Enumeración para tipos de evaluaciones
export enum EvaluationType {
  DELIVERABLE = "deliverable",
  PROJECT = "project",
  SPRINT = "sprint",
  PEER = "peer",
  SELF = "self",
  FINAL = "final",
}

// Interfaz para evaluación de criterios individuales
export interface CriterionEvaluation {
  criterionId: string;
  levelId: string;
  points: number;
  feedback?: string;
}

// Interfaz para evaluaciones de criterios
export interface CriteriaEvaluations {
  evaluations: CriterionEvaluation[];
  totalScore: number;
  maxPossibleScore: number;
}

// Interfaz para evaluaciones
export interface Evaluation {
  id: number;
  deliverableId: number | null;
  projectId: number | null;
  teamId: number | null;
  studentId: number | null;
  evaluatorId: number;
  rubricId: number | null;
  criteriaEvaluations: CriteriaEvaluations | null;
  totalScore: number;
  maxPossibleScore: number;
  overallFeedback: string | null;
  status: EvaluationStatus | string;
  createdAt: Date;
  updatedAt: Date;
}

// Interfaz extendida con datos relacionados
export interface EvaluationWithDetails extends Evaluation {
  deliverable?: {
    id: number;
    title: string;
    status: string;
  };
  project?: {
    id: number;
    name: string;
  };
  team?: {
    id: number;
    name: string;
  };
  student?: {
    id: number;
    name: string;
    email: string;
  };
  evaluator?: {
    id: number;
    name: string;
    email: string;
  };
  rubric?: {
    id: number;
    name: string;
    criteria: any;
  };
}

// Interfaz para crear una evaluación
export interface CreateEvaluationInput {
  deliverableId?: number;
  projectId?: number;
  teamId?: number;
  studentId?: number;
  evaluatorId: number;
  rubricId?: number;
  criteriaEvaluations?: CriteriaEvaluations;
  totalScore: number;
  maxPossibleScore: number;
  overallFeedback?: string;
  status?: EvaluationStatus | string;
}

// Interfaz para actualizar una evaluación
export interface UpdateEvaluationInput {
  criteriaEvaluations?: CriteriaEvaluations;
  totalScore?: number;
  maxPossibleScore?: number;
  overallFeedback?: string;
  status?: EvaluationStatus | string;
}

// Interfaz para filtros de búsqueda
export interface EvaluationFilters {
  deliverableId?: number;
  projectId?: number;
  teamId?: number;
  studentId?: number;
  evaluatorId?: number;
  rubricId?: number;
  status?: EvaluationStatus | string;
  minScore?: number;
  maxScore?: number;
}

// Interfaz para estadísticas de evaluaciones
export interface EvaluationStats {
  total: number;
  byStatus: Record<EvaluationStatus, number>;
  averageScore: number;
  highestScore: number;
  lowestScore: number;
  passRate: number;
  byEvaluator: Record<number, number>;
  byStudent: Record<number, number>;
}

// Interfaz para resumen de evaluación
export interface EvaluationSummary {
  evaluationId: number;
  studentName: string;
  deliverableTitle?: string;
  totalScore: number;
  maxPossibleScore: number;
  percentage: number;
  grade: string;
  status: string;
  evaluatedAt: Date;
}

// Tipos auxiliares
export type EvaluationFormData = Omit<CreateEvaluationInput, 'evaluatorId'>;
export type EvaluationUpdateData = Partial<UpdateEvaluationInput>;

// Validadores
export function isValidEvaluationStatus(status: string): status is EvaluationStatus {
  return Object.values(EvaluationStatus).includes(status as EvaluationStatus);
}

export function isValidEvaluationType(type: string): type is EvaluationType {
  return Object.values(EvaluationType).includes(type as EvaluationType);
}

export function validateCriteriaEvaluations(
  criteriaEvaluations: CriteriaEvaluations,
  rubricCriteria: any
): string[] {
  const errors: string[] = [];
  
  if (!criteriaEvaluations.evaluations || criteriaEvaluations.evaluations.length === 0) {
    errors.push("Debe evaluar al menos un criterio");
  }
  
  // Validar que todos los criterios requeridos estén evaluados
  if (rubricCriteria && rubricCriteria.criteria) {
    const requiredCriteria = rubricCriteria.criteria.map((c: any) => c.id);
    const evaluatedCriteria = criteriaEvaluations.evaluations.map(e => e.criterionId);
    
    const missingCriteria = requiredCriteria.filter((id: string) => !evaluatedCriteria.includes(id));
    if (missingCriteria.length > 0) {
      errors.push(`Faltan criterios por evaluar: ${missingCriteria.length}`);
    }
  }
  
  return errors;
}

// Funciones de utilidad
export function getEvaluationStatusColor(status: EvaluationStatus | string): string {
  switch (status) {
    case EvaluationStatus.DRAFT:
      return "gray";
    case EvaluationStatus.IN_PROGRESS:
      return "blue";
    case EvaluationStatus.COMPLETED:
      return "green";
    case EvaluationStatus.REVIEWED:
      return "purple";
    case EvaluationStatus.PUBLISHED:
      return "green";
    default:
      return "gray";
  }
}

export function calculatePercentage(evaluation: Evaluation): number {
  if (evaluation.maxPossibleScore === 0) return 0;
  return Math.round((evaluation.totalScore / evaluation.maxPossibleScore) * 100);
}

export function getLetterGrade(percentage: number): string {
  if (percentage >= 90) return "A";
  if (percentage >= 80) return "B";
  if (percentage >= 70) return "C";
  if (percentage >= 60) return "D";
  return "F";
}

export function getGradeColor(percentage: number): string {
  if (percentage >= 90) return "green";
  if (percentage >= 80) return "blue";
  if (percentage >= 70) return "yellow";
  if (percentage >= 60) return "orange";
  return "red";
}

export function isPassingGrade(percentage: number, passingThreshold: number = 60): boolean {
  return percentage >= passingThreshold;
}

export function calculateCriteriaScore(criteriaEvaluations: CriteriaEvaluations): number {
  return criteriaEvaluations.evaluations.reduce((total, evaluation) => total + evaluation.points, 0);
}

export function canEditEvaluation(evaluation: Evaluation, userId: number): boolean {
  // Solo el evaluador puede editar si no está publicada
  if (evaluation.evaluatorId !== userId) return false;
  return evaluation.status !== EvaluationStatus.PUBLISHED;
}

export function canPublishEvaluation(evaluation: Evaluation): boolean {
  return [
    EvaluationStatus.COMPLETED,
    EvaluationStatus.REVIEWED,
  ].includes(evaluation.status as EvaluationStatus);
}

// Constantes
export const EVALUATION_STATUS_OPTIONS = [
  { value: EvaluationStatus.DRAFT, label: "Borrador", color: "gray" },
  { value: EvaluationStatus.IN_PROGRESS, label: "En progreso", color: "blue" },
  { value: EvaluationStatus.COMPLETED, label: "Completada", color: "green" },
  { value: EvaluationStatus.REVIEWED, label: "Revisada", color: "purple" },
  { value: EvaluationStatus.PUBLISHED, label: "Publicada", color: "green" },
];

export const EVALUATION_TYPE_OPTIONS = [
  { value: EvaluationType.DELIVERABLE, label: "Entregable", icon: "📦" },
  { value: EvaluationType.PROJECT, label: "Proyecto", icon: "🏗️" },
  { value: EvaluationType.SPRINT, label: "Sprint", icon: "🏃" },
  { value: EvaluationType.PEER, label: "Entre pares", icon: "👥" },
  { value: EvaluationType.SELF, label: "Autoevaluación", icon: "🪞" },
  { value: EvaluationType.FINAL, label: "Final", icon: "🎯" },
];

export const GRADE_SCALE = [
  { min: 90, max: 100, letter: "A", description: "Excelente", color: "green" },
  { min: 80, max: 89, letter: "B", description: "Bueno", color: "blue" },
  { min: 70, max: 79, letter: "C", description: "Regular", color: "yellow" },
  { min: 60, max: 69, letter: "D", description: "Suficiente", color: "orange" },
  { min: 0, max: 59, letter: "F", description: "Insuficiente", color: "red" },
];
