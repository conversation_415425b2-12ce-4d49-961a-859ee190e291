// Deliverable model types and utilities

export interface Deliverable {
  id: number;
  title: string;
  description?: string;
  type: DeliverableType;
  status: DeliverableStatus;
  dueDate?: Date;
  submittedAt?: Date;
  projectId: number;
  userStoryId?: number;
  sprintId?: number;
  submittedBy?: number;
  createdBy?: number;
  createdAt: Date;
  updatedAt: Date;
}

export enum DeliverableType {
  DOCUMENT = "document",
  CODE = "code",
  PRESENTATION = "presentation",
  PROTOTYPE = "prototype",
  REPORT = "report",
  DESIGN = "design",
  TEST = "test",
  OTHER = "other"
}

export enum DeliverableStatus {
  PENDING = "pending",
  IN_PROGRESS = "in_progress",
  SUBMITTED = "submitted",
  REVIEWED = "reviewed",
  APPROVED = "approved",
  REJECTED = "rejected",
  LATE = "late"
}

export interface DeliverableFile {
  id: number;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  path: string;
  deliverableId: number;
  uploadedBy: number;
  createdAt: Date;
}

export interface CreateDeliverableInput {
  title: string;
  description?: string;
  type: DeliverableType;
  dueDate?: Date;
  projectId: number;
  userStoryId?: number;
  sprintId?: number;
}

export interface UpdateDeliverableInput {
  title?: string;
  description?: string;
  type?: DeliverableType;
  status?: DeliverableStatus;
  dueDate?: Date;
  submittedAt?: Date;
  userStoryId?: number;
  sprintId?: number;
}

// Type options for forms
export const DELIVERABLE_TYPE_OPTIONS = [
  { value: DeliverableType.DOCUMENT, label: "Documento" },
  { value: DeliverableType.CODE, label: "Código" },
  { value: DeliverableType.PRESENTATION, label: "Presentación" },
  { value: DeliverableType.PROTOTYPE, label: "Prototipo" },
  { value: DeliverableType.REPORT, label: "Reporte" },
  { value: DeliverableType.DESIGN, label: "Diseño" },
  { value: DeliverableType.TEST, label: "Pruebas" },
  { value: DeliverableType.OTHER, label: "Otro" },
];

// Status options for forms
export const DELIVERABLE_STATUS_OPTIONS = [
  { value: DeliverableStatus.PENDING, label: "Pendiente" },
  { value: DeliverableStatus.IN_PROGRESS, label: "En Progreso" },
  { value: DeliverableStatus.SUBMITTED, label: "Entregado" },
  { value: DeliverableStatus.REVIEWED, label: "Revisado" },
  { value: DeliverableStatus.APPROVED, label: "Aprobado" },
  { value: DeliverableStatus.REJECTED, label: "Rechazado" },
  { value: DeliverableStatus.LATE, label: "Atrasado" },
];

// Utility functions
export function getDeliverableTypeLabel(type: DeliverableType): string {
  const option = DELIVERABLE_TYPE_OPTIONS.find(opt => opt.value === type);
  return option?.label || type;
}

export function getDeliverableStatusLabel(status: DeliverableStatus): string {
  const option = DELIVERABLE_STATUS_OPTIONS.find(opt => opt.value === status);
  return option?.label || status;
}

export function getDeliverableTypeColor(type: DeliverableType): string {
  switch (type) {
    case DeliverableType.DOCUMENT:
      return "text-blue-600 bg-blue-100";
    case DeliverableType.CODE:
      return "text-green-600 bg-green-100";
    case DeliverableType.PRESENTATION:
      return "text-purple-600 bg-purple-100";
    case DeliverableType.PROTOTYPE:
      return "text-orange-600 bg-orange-100";
    case DeliverableType.REPORT:
      return "text-indigo-600 bg-indigo-100";
    case DeliverableType.DESIGN:
      return "text-pink-600 bg-pink-100";
    case DeliverableType.TEST:
      return "text-red-600 bg-red-100";
    case DeliverableType.OTHER:
      return "text-gray-600 bg-gray-100";
    default:
      return "text-gray-600 bg-gray-100";
  }
}

export function getDeliverableStatusColor(status: DeliverableStatus): string {
  switch (status) {
    case DeliverableStatus.PENDING:
      return "text-gray-600 bg-gray-100";
    case DeliverableStatus.IN_PROGRESS:
      return "text-yellow-600 bg-yellow-100";
    case DeliverableStatus.SUBMITTED:
      return "text-blue-600 bg-blue-100";
    case DeliverableStatus.REVIEWED:
      return "text-purple-600 bg-purple-100";
    case DeliverableStatus.APPROVED:
      return "text-green-600 bg-green-100";
    case DeliverableStatus.REJECTED:
      return "text-red-600 bg-red-100";
    case DeliverableStatus.LATE:
      return "text-red-600 bg-red-100";
    default:
      return "text-gray-600 bg-gray-100";
  }
}

// Validation functions
export function validateDeliverable(deliverable: Partial<CreateDeliverableInput>): string[] {
  const errors: string[] = [];

  if (!deliverable.title?.trim()) {
    errors.push("El título es obligatorio");
  }

  if (!deliverable.type) {
    errors.push("El tipo es obligatorio");
  }

  if (!deliverable.projectId) {
    errors.push("El proyecto es obligatorio");
  }

  if (deliverable.dueDate && deliverable.dueDate < new Date()) {
    errors.push("La fecha de vencimiento no puede ser en el pasado");
  }

  return errors;
}

// Date utilities
export function isDeliverableOverdue(deliverable: Deliverable): boolean {
  if (!deliverable.dueDate) return false;
  if (deliverable.status === DeliverableStatus.SUBMITTED || 
      deliverable.status === DeliverableStatus.APPROVED) return false;
  
  return new Date() > deliverable.dueDate;
}

export function getDaysUntilDue(deliverable: Deliverable): number | null {
  if (!deliverable.dueDate) return null;
  
  const now = new Date();
  const dueDate = new Date(deliverable.dueDate);
  const diffTime = dueDate.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return diffDays;
}

export function formatDueDate(deliverable: Deliverable): string {
  if (!deliverable.dueDate) return "Sin fecha límite";
  
  const daysUntilDue = getDaysUntilDue(deliverable);
  if (daysUntilDue === null) return "Sin fecha límite";
  
  const dueDate = new Date(deliverable.dueDate);
  const formattedDate = dueDate.toLocaleDateString("es-ES", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
  
  if (daysUntilDue < 0) {
    return `${formattedDate} (${Math.abs(daysUntilDue)} días atrasado)`;
  } else if (daysUntilDue === 0) {
    return `${formattedDate} (Vence hoy)`;
  } else if (daysUntilDue === 1) {
    return `${formattedDate} (Vence mañana)`;
  } else {
    return `${formattedDate} (${daysUntilDue} días restantes)`;
  }
}

// File utilities
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes";
  
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

export function getFileIcon(mimeType: string): string {
  if (mimeType.startsWith("image/")) return "🖼️";
  if (mimeType.startsWith("video/")) return "🎥";
  if (mimeType.startsWith("audio/")) return "🎵";
  if (mimeType.includes("pdf")) return "📄";
  if (mimeType.includes("word")) return "📝";
  if (mimeType.includes("excel") || mimeType.includes("spreadsheet")) return "📊";
  if (mimeType.includes("powerpoint") || mimeType.includes("presentation")) return "📽️";
  if (mimeType.includes("zip") || mimeType.includes("rar")) return "🗜️";
  return "📎";
}

// Deliverable with related data
export interface DeliverableWithDetails extends Deliverable {
  project?: {
    id: number;
    name: string;
  };
  userStory?: {
    id: number;
    title: string;
  };
  sprint?: {
    id: number;
    name: string;
  };
  submitter?: {
    id: number;
    name: string;
  };
  files?: DeliverableFile[];
  evaluationsCount?: number;
}
