// Enumeración para estados de entregables
export enum DeliverableStatus {
  PENDING = "pending",
  IN_PROGRESS = "in_progress",
  SUBMITTED = "submitted",
  UNDER_REVIEW = "under_review",
  REVIEWED = "reviewed",
  APPROVED = "approved",
  REJECTED = "rejected",
  RESUBMITTED = "resubmitted",
}

// Enumeración para tipos de entregables
export enum DeliverableType {
  DOCUMENT = "document",
  CODE = "code",
  PRESENTATION = "presentation",
  PROTOTYPE = "prototype",
  REPORT = "report",
  DESIGN = "design",
  TEST = "test",
  OTHER = "other",
}

// Interfaz para entregables
export interface Deliverable {
  id: number;
  title: string;
  description: string | null;
  projectId: number;
  sprintId: number | null;
  userStoryId: number | null;
  dueDate: Date | null;
  submittedAt: Date | null;
  status: DeliverableStatus | string;
  submittedBy: number | null;
  createdBy: number | null;
  createdAt: Date;
  updatedAt: Date;
}

// Interfaz extendida con datos relacionados
export interface DeliverableWithDetails extends Deliverable {
  project?: {
    id: number;
    name: string;
  };
  sprint?: {
    id: number;
    name: string;
  };
  userStory?: {
    id: number;
    title: string;
  };
  submitter?: {
    id: number;
    name: string;
    email: string;
  };
  creator?: {
    id: number;
    name: string;
    email: string;
  };
  evaluations?: Evaluation[];
  files?: DeliverableFile[];
}

// Interfaz para archivos de entregables
export interface DeliverableFile {
  id: number;
  deliverableId: number;
  fileName: string;
  filePath: string;
  fileSize: number;
  mimeType: string;
  uploadedBy: number;
  uploadedAt: Date;
}

// Interfaz para crear un entregable
export interface CreateDeliverableInput {
  title: string;
  description?: string;
  projectId: number;
  sprintId?: number;
  userStoryId?: number;
  dueDate?: Date;
  status?: DeliverableStatus | string;
  createdBy?: number;
}

// Interfaz para actualizar un entregable
export interface UpdateDeliverableInput {
  title?: string;
  description?: string;
  sprintId?: number;
  userStoryId?: number;
  dueDate?: Date;
  status?: DeliverableStatus | string;
}

// Interfaz para enviar un entregable
export interface SubmitDeliverableInput {
  submittedBy: number;
  files?: File[];
  notes?: string;
}

// Interfaz para filtros de búsqueda
export interface DeliverableFilters {
  projectId?: number;
  sprintId?: number;
  userStoryId?: number;
  status?: DeliverableStatus | string;
  submittedBy?: number;
  createdBy?: number;
  dueDateFrom?: Date;
  dueDateTo?: Date;
  overdue?: boolean;
}

// Interfaz para estadísticas de entregables
export interface DeliverableStats {
  total: number;
  byStatus: Record<DeliverableStatus, number>;
  submitted: number;
  pending: number;
  overdue: number;
  averageSubmissionTime: number;
  onTimeSubmissions: number;
  lateSubmissions: number;
}

// Tipos auxiliares
export type DeliverableFormData = Omit<CreateDeliverableInput, 'projectId' | 'createdBy'>;
export type DeliverableUpdateData = Partial<UpdateDeliverableInput>;

// Validadores
export function isValidDeliverableStatus(status: string): status is DeliverableStatus {
  return Object.values(DeliverableStatus).includes(status as DeliverableStatus);
}

export function isValidDeliverableType(type: string): type is DeliverableType {
  return Object.values(DeliverableType).includes(type as DeliverableType);
}

// Funciones de utilidad
export function getDeliverableStatusColor(status: DeliverableStatus | string): string {
  switch (status) {
    case DeliverableStatus.PENDING:
      return "gray";
    case DeliverableStatus.IN_PROGRESS:
      return "blue";
    case DeliverableStatus.SUBMITTED:
      return "green";
    case DeliverableStatus.UNDER_REVIEW:
      return "yellow";
    case DeliverableStatus.REVIEWED:
      return "purple";
    case DeliverableStatus.APPROVED:
      return "green";
    case DeliverableStatus.REJECTED:
      return "red";
    case DeliverableStatus.RESUBMITTED:
      return "orange";
    default:
      return "gray";
  }
}

export function isDeliverableOverdue(deliverable: Deliverable): boolean {
  if (!deliverable.dueDate) return false;
  if (deliverable.status === DeliverableStatus.SUBMITTED || 
      deliverable.status === DeliverableStatus.APPROVED) return false;
  return new Date() > new Date(deliverable.dueDate);
}

export function isDeliverableSubmitted(deliverable: Deliverable): boolean {
  return [
    DeliverableStatus.SUBMITTED,
    DeliverableStatus.UNDER_REVIEW,
    DeliverableStatus.REVIEWED,
    DeliverableStatus.APPROVED,
    DeliverableStatus.RESUBMITTED,
  ].includes(deliverable.status as DeliverableStatus);
}

export function canEditDeliverable(deliverable: Deliverable, userId: number): boolean {
  // Solo el creador puede editar si no ha sido enviado
  if (deliverable.createdBy !== userId) return false;
  return !isDeliverableSubmitted(deliverable);
}

export function canSubmitDeliverable(deliverable: Deliverable): boolean {
  return [
    DeliverableStatus.PENDING,
    DeliverableStatus.IN_PROGRESS,
    DeliverableStatus.REJECTED,
  ].includes(deliverable.status as DeliverableStatus);
}

export function getDaysUntilDue(deliverable: Deliverable): number | null {
  if (!deliverable.dueDate) return null;
  const now = new Date();
  const dueDate = new Date(deliverable.dueDate);
  const diffTime = dueDate.getTime() - now.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

export function getSubmissionDelay(deliverable: Deliverable): number | null {
  if (!deliverable.dueDate || !deliverable.submittedAt) return null;
  const dueDate = new Date(deliverable.dueDate);
  const submittedDate = new Date(deliverable.submittedAt);
  const diffTime = submittedDate.getTime() - dueDate.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

// Constantes
export const DELIVERABLE_STATUS_OPTIONS = [
  { value: DeliverableStatus.PENDING, label: "Pendiente", color: "gray" },
  { value: DeliverableStatus.IN_PROGRESS, label: "En progreso", color: "blue" },
  { value: DeliverableStatus.SUBMITTED, label: "Enviado", color: "green" },
  { value: DeliverableStatus.UNDER_REVIEW, label: "En revisión", color: "yellow" },
  { value: DeliverableStatus.REVIEWED, label: "Revisado", color: "purple" },
  { value: DeliverableStatus.APPROVED, label: "Aprobado", color: "green" },
  { value: DeliverableStatus.REJECTED, label: "Rechazado", color: "red" },
  { value: DeliverableStatus.RESUBMITTED, label: "Reenviado", color: "orange" },
];

export const DELIVERABLE_TYPE_OPTIONS = [
  { value: DeliverableType.DOCUMENT, label: "Documento", icon: "📄" },
  { value: DeliverableType.CODE, label: "Código", icon: "💻" },
  { value: DeliverableType.PRESENTATION, label: "Presentación", icon: "📊" },
  { value: DeliverableType.PROTOTYPE, label: "Prototipo", icon: "🔧" },
  { value: DeliverableType.REPORT, label: "Reporte", icon: "📋" },
  { value: DeliverableType.DESIGN, label: "Diseño", icon: "🎨" },
  { value: DeliverableType.TEST, label: "Prueba", icon: "🧪" },
  { value: DeliverableType.OTHER, label: "Otro", icon: "📦" },
];

// Importaciones de tipos relacionados
interface Evaluation {
  id: number;
  totalScore: number;
  maxPossibleScore: number;
}
