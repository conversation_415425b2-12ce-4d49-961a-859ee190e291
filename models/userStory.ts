// Enumeración para prioridades de User Stories
export enum UserStoryPriority {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
  CRITICAL = "critical",
}

// Enumeración para estados de User Stories
export enum UserStoryStatus {
  BACKLOG = "backlog",
  READY = "ready",
  IN_PROGRESS = "in_progress",
  IN_REVIEW = "in_review",
  DONE = "done",
  CANCELLED = "cancelled",
}

// Interfaz para User Stories
export interface UserStory {
  id: number;
  title: string;
  description: string;
  acceptanceCriteria: string;
  priority: UserStoryPriority | string;
  points: number | null;
  projectId: number;
  sprintId: number | null;
  status: UserStoryStatus | string;
  createdBy: number | null;
  createdAt: Date;
  updatedAt: Date;
}

// Interfaz extendida con datos relacionados
export interface UserStoryWithDetails extends UserStory {
  project?: {
    id: number;
    name: string;
  };
  sprint?: {
    id: number;
    name: string;
  };
  creator?: {
    id: number;
    name: string;
    email: string;
  };
  tasks?: Task[];
  deliverables?: Deliverable[];
}

// Interfaz para crear una User Story
export interface CreateUserStoryInput {
  title: string;
  description: string;
  acceptanceCriteria: string;
  priority?: UserStoryPriority | string;
  points?: number;
  projectId: number;
  sprintId?: number;
  status?: UserStoryStatus | string;
  createdBy?: number;
}

// Interfaz para actualizar una User Story
export interface UpdateUserStoryInput {
  title?: string;
  description?: string;
  acceptanceCriteria?: string;
  priority?: UserStoryPriority | string;
  points?: number;
  sprintId?: number;
  status?: UserStoryStatus | string;
}

// Interfaz para filtros de búsqueda
export interface UserStoryFilters {
  projectId?: number;
  sprintId?: number;
  status?: UserStoryStatus | string;
  priority?: UserStoryPriority | string;
  createdBy?: number;
}

// Interfaz para estadísticas de User Stories
export interface UserStoryStats {
  total: number;
  byStatus: Record<UserStoryStatus, number>;
  byPriority: Record<UserStoryPriority, number>;
  totalPoints: number;
  completedPoints: number;
  averagePoints: number;
}

// Tipos auxiliares
export type UserStoryFormData = Omit<CreateUserStoryInput, 'projectId' | 'createdBy'>;
export type UserStoryUpdateData = Partial<UpdateUserStoryInput>;

// Validadores
export function isValidUserStoryPriority(priority: string): priority is UserStoryPriority {
  return Object.values(UserStoryPriority).includes(priority as UserStoryPriority);
}

export function isValidUserStoryStatus(status: string): status is UserStoryStatus {
  return Object.values(UserStoryStatus).includes(status as UserStoryStatus);
}

// Funciones de utilidad
export function getUserStoryPriorityWeight(priority: UserStoryPriority | string): number {
  switch (priority) {
    case UserStoryPriority.CRITICAL:
      return 4;
    case UserStoryPriority.HIGH:
      return 3;
    case UserStoryPriority.MEDIUM:
      return 2;
    case UserStoryPriority.LOW:
      return 1;
    default:
      return 2; // Default to medium
  }
}

export function getUserStoryStatusColor(status: UserStoryStatus | string): string {
  switch (status) {
    case UserStoryStatus.BACKLOG:
      return "gray";
    case UserStoryStatus.READY:
      return "blue";
    case UserStoryStatus.IN_PROGRESS:
      return "yellow";
    case UserStoryStatus.IN_REVIEW:
      return "orange";
    case UserStoryStatus.DONE:
      return "green";
    case UserStoryStatus.CANCELLED:
      return "red";
    default:
      return "gray";
  }
}

export function getUserStoryPriorityColor(priority: UserStoryPriority | string): string {
  switch (priority) {
    case UserStoryPriority.CRITICAL:
      return "red";
    case UserStoryPriority.HIGH:
      return "orange";
    case UserStoryPriority.MEDIUM:
      return "yellow";
    case UserStoryPriority.LOW:
      return "green";
    default:
      return "yellow";
  }
}

// Constantes para opciones de UI
export const USER_STORY_PRIORITY_OPTIONS = [
  { value: UserStoryPriority.LOW, label: "Baja", color: "green" },
  { value: UserStoryPriority.MEDIUM, label: "Media", color: "yellow" },
  { value: UserStoryPriority.HIGH, label: "Alta", color: "orange" },
  { value: UserStoryPriority.CRITICAL, label: "Crítica", color: "red" },
];

export const USER_STORY_STATUS_OPTIONS = [
  { value: UserStoryStatus.BACKLOG, label: "Backlog", color: "gray" },
  { value: UserStoryStatus.READY, label: "Listo", color: "blue" },
  { value: UserStoryStatus.IN_PROGRESS, label: "En progreso", color: "yellow" },
  { value: UserStoryStatus.IN_REVIEW, label: "En revisión", color: "orange" },
  { value: UserStoryStatus.DONE, label: "Completado", color: "green" },
  { value: UserStoryStatus.CANCELLED, label: "Cancelado", color: "red" },
];

// Importaciones de tipos relacionados (se definirán en otros archivos)
interface Task {
  id: number;
  title: string;
  status: string;
}

interface Deliverable {
  id: number;
  title: string;
  status: string;
}
