// User Story model types and utilities

export interface UserStory {
  id: number;
  title: string;
  description: string;
  acceptanceCriteria: string;
  priority: UserStoryPriority;
  status: UserStoryStatus;
  points?: number;
  projectId: number;
  sprintId?: number;
  createdBy?: number;
  createdAt: Date;
  updatedAt: Date;
}

export enum UserStoryPriority {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
  CRITICAL = "critical"
}

export enum UserStoryStatus {
  BACKLOG = "backlog",
  READY = "ready",
  IN_PROGRESS = "in_progress",
  REVIEW = "review",
  DONE = "done",
  CANCELLED = "cancelled"
}

export interface CreateUserStoryInput {
  title: string;
  description: string;
  acceptanceCriteria: string;
  priority: UserStoryPriority;
  points?: number;
  projectId: number;
  sprintId?: number;
}

export interface UpdateUserStoryInput {
  title?: string;
  description?: string;
  acceptanceCriteria?: string;
  priority?: UserStoryPriority;
  status?: UserStoryStatus;
  points?: number;
  sprintId?: number;
}

// Priority options for forms
export const USER_STORY_PRIORITY_OPTIONS = [
  { value: UserStoryPriority.LOW, label: "Baja" },
  { value: UserStoryPriority.MEDIUM, label: "Media" },
  { value: UserStoryPriority.HIGH, label: "Alta" },
  { value: UserStoryPriority.CRITICAL, label: "Crítica" },
];

// Status options for forms
export const USER_STORY_STATUS_OPTIONS = [
  { value: UserStoryStatus.BACKLOG, label: "Backlog" },
  { value: UserStoryStatus.READY, label: "Lista" },
  { value: UserStoryStatus.IN_PROGRESS, label: "En Progreso" },
  { value: UserStoryStatus.REVIEW, label: "En Revisión" },
  { value: UserStoryStatus.DONE, label: "Completada" },
  { value: UserStoryStatus.CANCELLED, label: "Cancelada" },
];

// Utility functions
export function getUserStoryPriorityLabel(priority: UserStoryPriority): string {
  const option = USER_STORY_PRIORITY_OPTIONS.find(opt => opt.value === priority);
  return option?.label || priority;
}

export function getUserStoryStatusLabel(status: UserStoryStatus): string {
  const option = USER_STORY_STATUS_OPTIONS.find(opt => opt.value === status);
  return option?.label || status;
}

export function getUserStoryPriorityColor(priority: UserStoryPriority): string {
  switch (priority) {
    case UserStoryPriority.LOW:
      return "text-green-600 bg-green-100";
    case UserStoryPriority.MEDIUM:
      return "text-yellow-600 bg-yellow-100";
    case UserStoryPriority.HIGH:
      return "text-orange-600 bg-orange-100";
    case UserStoryPriority.CRITICAL:
      return "text-red-600 bg-red-100";
    default:
      return "text-gray-600 bg-gray-100";
  }
}

export function getUserStoryStatusColor(status: UserStoryStatus): string {
  switch (status) {
    case UserStoryStatus.BACKLOG:
      return "text-gray-600 bg-gray-100";
    case UserStoryStatus.READY:
      return "text-blue-600 bg-blue-100";
    case UserStoryStatus.IN_PROGRESS:
      return "text-yellow-600 bg-yellow-100";
    case UserStoryStatus.REVIEW:
      return "text-purple-600 bg-purple-100";
    case UserStoryStatus.DONE:
      return "text-green-600 bg-green-100";
    case UserStoryStatus.CANCELLED:
      return "text-red-600 bg-red-100";
    default:
      return "text-gray-600 bg-gray-100";
  }
}

// Validation functions
export function validateUserStory(userStory: Partial<CreateUserStoryInput>): string[] {
  const errors: string[] = [];

  if (!userStory.title?.trim()) {
    errors.push("El título es obligatorio");
  }

  if (!userStory.description?.trim()) {
    errors.push("La descripción es obligatoria");
  }

  if (!userStory.acceptanceCriteria?.trim()) {
    errors.push("Los criterios de aceptación son obligatorios");
  }

  if (!userStory.projectId) {
    errors.push("El proyecto es obligatorio");
  }

  if (userStory.points !== undefined && (userStory.points < 0 || userStory.points > 100)) {
    errors.push("Los puntos deben estar entre 0 y 100");
  }

  return errors;
}

// Story points validation (Fibonacci sequence)
export const VALID_STORY_POINTS = [1, 2, 3, 5, 8, 13, 21, 34, 55, 89];

export function isValidStoryPoints(points: number): boolean {
  return VALID_STORY_POINTS.includes(points);
}

export function getStoryPointsOptions() {
  return VALID_STORY_POINTS.map(points => ({
    value: points.toString(),
    label: `${points} punto${points !== 1 ? 's' : ''}`,
  }));
}

// User Story with related data
export interface UserStoryWithDetails extends UserStory {
  project?: {
    id: number;
    name: string;
  };
  sprint?: {
    id: number;
    name: string;
  };
  creator?: {
    id: number;
    name: string;
  };
  tasksCount?: number;
  completedTasksCount?: number;
}
