// Enumeración para tipos de reportes
export enum ReportType {
  PROGRESS = "progress",
  PERFORMANCE = "performance",
  SUMMARY = "summary",
  BURNDOWN = "burndown",
  VELOCITY = "velocity",
  QUALITY = "quality",
  TEAM = "team",
  CUSTOM = "custom",
}

// Enumeración para formatos de reportes
export enum ReportFormat {
  JSON = "json",
  PDF = "pdf",
  EXCEL = "excel",
  CSV = "csv",
  HTML = "html",
}

// Interfaz para datos de reporte de progreso
export interface ProgressReportData {
  period: {
    start: Date;
    end: Date;
  };
  summary: {
    totalTasks: number;
    completedTasks: number;
    inProgressTasks: number;
    blockedTasks: number;
    completionRate: number;
  };
  userStories: {
    total: number;
    completed: number;
    inProgress: number;
    backlog: number;
  };
  sprints: {
    active: number;
    completed: number;
    planned: number;
  };
  deliverables: {
    submitted: number;
    pending: number;
    overdue: number;
  };
}

// Interfaz para datos de reporte de rendimiento
export interface PerformanceReportData {
  period: {
    start: Date;
    end: Date;
  };
  velocity: {
    current: number;
    average: number;
    trend: "up" | "down" | "stable";
  };
  efficiency: {
    estimatedVsActual: number;
    taskCompletionRate: number;
    averageTaskTime: number;
  };
  quality: {
    defectRate: number;
    reworkRate: number;
    testCoverage: number;
  };
  team: {
    productivity: number;
    collaboration: number;
    satisfaction: number;
  };
}

// Interfaz para reportes
export interface Report {
  id: number;
  title: string;
  description: string | null;
  projectId: number;
  type: ReportType | string;
  data: any; // JSON data específico del tipo de reporte
  generatedBy: number | null;
  createdAt: Date;
  updatedAt: Date;
}

// Interfaz extendida con datos relacionados
export interface ReportWithDetails extends Report {
  project?: {
    id: number;
    name: string;
  };
  generator?: {
    id: number;
    name: string;
    email: string;
  };
}

// Interfaz para crear un reporte
export interface CreateReportInput {
  title: string;
  description?: string;
  projectId: number;
  type: ReportType | string;
  data: any;
  generatedBy?: number;
}

// Interfaz para actualizar un reporte
export interface UpdateReportInput {
  title?: string;
  description?: string;
  type?: ReportType | string;
  data?: any;
}

// Interfaz para filtros de búsqueda
export interface ReportFilters {
  projectId?: number;
  type?: ReportType | string;
  generatedBy?: number;
  createdFrom?: Date;
  createdTo?: Date;
}

// Interfaz para configuración de reporte
export interface ReportConfig {
  type: ReportType;
  projectId: number;
  period?: {
    start: Date;
    end: Date;
  };
  includeCharts?: boolean;
  includeDetails?: boolean;
  format?: ReportFormat;
  filters?: any;
}

// Interfaz para estadísticas de reportes
export interface ReportStats {
  total: number;
  byType: Record<ReportType, number>;
  byProject: Record<number, number>;
  mostGenerated: ReportType;
  averageGenerationTime: number;
}

// Tipos auxiliares
export type ReportFormData = Omit<CreateReportInput, 'generatedBy'>;
export type ReportUpdateData = Partial<UpdateReportInput>;

// Validadores
export function isValidReportType(type: string): type is ReportType {
  return Object.values(ReportType).includes(type as ReportType);
}

export function isValidReportFormat(format: string): format is ReportFormat {
  return Object.values(ReportFormat).includes(format as ReportFormat);
}

export function validateReportData(type: ReportType, data: any): string[] {
  const errors: string[] = [];
  
  switch (type) {
    case ReportType.PROGRESS:
      if (!data.period || !data.period.start || !data.period.end) {
        errors.push("El reporte de progreso debe incluir un período válido");
      }
      break;
    case ReportType.PERFORMANCE:
      if (!data.velocity || typeof data.velocity.current !== 'number') {
        errors.push("El reporte de rendimiento debe incluir datos de velocidad");
      }
      break;
    case ReportType.BURNDOWN:
      if (!data.burndownData || !Array.isArray(data.burndownData)) {
        errors.push("El reporte burndown debe incluir datos de burndown");
      }
      break;
  }
  
  return errors;
}

// Funciones de utilidad
export function getReportTypeIcon(type: ReportType | string): string {
  switch (type) {
    case ReportType.PROGRESS:
      return "📊";
    case ReportType.PERFORMANCE:
      return "⚡";
    case ReportType.SUMMARY:
      return "📋";
    case ReportType.BURNDOWN:
      return "📉";
    case ReportType.VELOCITY:
      return "🚀";
    case ReportType.QUALITY:
      return "✅";
    case ReportType.TEAM:
      return "👥";
    case ReportType.CUSTOM:
      return "🔧";
    default:
      return "📄";
  }
}

export function getReportTypeColor(type: ReportType | string): string {
  switch (type) {
    case ReportType.PROGRESS:
      return "blue";
    case ReportType.PERFORMANCE:
      return "green";
    case ReportType.SUMMARY:
      return "purple";
    case ReportType.BURNDOWN:
      return "orange";
    case ReportType.VELOCITY:
      return "red";
    case ReportType.QUALITY:
      return "green";
    case ReportType.TEAM:
      return "blue";
    case ReportType.CUSTOM:
      return "gray";
    default:
      return "gray";
  }
}

export function generateReportTitle(type: ReportType, projectName: string, period?: { start: Date; end: Date }): string {
  const typeLabels = {
    [ReportType.PROGRESS]: "Reporte de Progreso",
    [ReportType.PERFORMANCE]: "Reporte de Rendimiento",
    [ReportType.SUMMARY]: "Reporte Resumen",
    [ReportType.BURNDOWN]: "Reporte Burndown",
    [ReportType.VELOCITY]: "Reporte de Velocidad",
    [ReportType.QUALITY]: "Reporte de Calidad",
    [ReportType.TEAM]: "Reporte de Equipo",
    [ReportType.CUSTOM]: "Reporte Personalizado",
  };
  
  let title = `${typeLabels[type]} - ${projectName}`;
  
  if (period) {
    const startStr = period.start.toLocaleDateString();
    const endStr = period.end.toLocaleDateString();
    title += ` (${startStr} - ${endStr})`;
  }
  
  return title;
}

export function canEditReport(report: Report, userId: number): boolean {
  // Solo el generador puede editar el reporte
  return report.generatedBy === userId;
}

export function canDeleteReport(report: Report, userId: number): boolean {
  // Solo el generador puede eliminar el reporte
  return report.generatedBy === userId;
}

export function getReportAge(report: Report): number {
  const now = new Date();
  const created = new Date(report.createdAt);
  return Math.floor((now.getTime() - created.getTime()) / (1000 * 60 * 60 * 24));
}

// Constantes
export const REPORT_TYPE_OPTIONS = [
  { value: ReportType.PROGRESS, label: "Progreso", icon: "📊", color: "blue" },
  { value: ReportType.PERFORMANCE, label: "Rendimiento", icon: "⚡", color: "green" },
  { value: ReportType.SUMMARY, label: "Resumen", icon: "📋", color: "purple" },
  { value: ReportType.BURNDOWN, label: "Burndown", icon: "📉", color: "orange" },
  { value: ReportType.VELOCITY, label: "Velocidad", icon: "🚀", color: "red" },
  { value: ReportType.QUALITY, label: "Calidad", icon: "✅", color: "green" },
  { value: ReportType.TEAM, label: "Equipo", icon: "👥", color: "blue" },
  { value: ReportType.CUSTOM, label: "Personalizado", icon: "🔧", color: "gray" },
];

export const REPORT_FORMAT_OPTIONS = [
  { value: ReportFormat.JSON, label: "JSON", extension: ".json" },
  { value: ReportFormat.PDF, label: "PDF", extension: ".pdf" },
  { value: ReportFormat.EXCEL, label: "Excel", extension: ".xlsx" },
  { value: ReportFormat.CSV, label: "CSV", extension: ".csv" },
  { value: ReportFormat.HTML, label: "HTML", extension: ".html" },
];

export const DEFAULT_REPORT_PERIOD_DAYS = 30;
export const MAX_REPORT_AGE_DAYS = 365;
