// Export all models and types

// Project models (existing)
export * from "./project.ts";

// User models
export * from "./user.ts";

// User Story models
export * from "./userStory.ts";

// Task models
export * from "./task.ts";

// Sprint models
export * from "./sprint.ts";

// Rubric models
export * from "./rubric.ts";

// Deliverable models
export * from "./deliverable.ts";

// Evaluation models
export * from "./evaluation.ts";

// Report models
export * from "./report.ts";

// Backlog Item models
export * from "./backlogItem.ts";

// Common types and utilities
export interface BaseEntity {
  id: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface BaseEntityWithCreator extends BaseEntity {
  createdBy: number | null;
}

export interface PaginationParams {
  page: number;
  limit: number;
  offset?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface SortParams {
  field: string;
  direction: "asc" | "desc";
}

export interface SearchParams {
  query: string;
  fields?: string[];
}

export interface DateRange {
  start: Date;
  end: Date;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface ValidationError {
  field: string;
  message: string;
}

export interface FormState<T> {
  data: T;
  errors: Record<string, string>;
  isSubmitting: boolean;
  isValid: boolean;
}

// Common enums
export enum Priority {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
  CRITICAL = "critical",
}

export enum Status {
  ACTIVE = "active",
  INACTIVE = "inactive",
  PENDING = "pending",
  COMPLETED = "completed",
  CANCELLED = "cancelled",
}

// Utility types
export type ID = number;
export type Timestamp = Date;
export type JSONValue = string | number | boolean | null | JSONObject | JSONArray;
export type JSONObject = { [key: string]: JSONValue };
export type JSONArray = JSONValue[];

// Common validation functions
export function isValidId(id: any): id is number {
  return typeof id === 'number' && id > 0 && Number.isInteger(id);
}

export function isValidDate(date: any): date is Date {
  return date instanceof Date && !isNaN(date.getTime());
}

export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

// Common utility functions
export function formatDate(date: Date, locale: string = 'es-ES'): string {
  return date.toLocaleDateString(locale);
}

export function formatDateTime(date: Date, locale: string = 'es-ES'): string {
  return date.toLocaleString(locale);
}

export function formatRelativeTime(date: Date, locale: string = 'es-ES'): string {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  if (diffInSeconds < 60) return 'hace un momento';
  if (diffInSeconds < 3600) return `hace ${Math.floor(diffInSeconds / 60)} minutos`;
  if (diffInSeconds < 86400) return `hace ${Math.floor(diffInSeconds / 3600)} horas`;
  if (diffInSeconds < 2592000) return `hace ${Math.floor(diffInSeconds / 86400)} días`;
  
  return formatDate(date, locale);
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3) + '...';
}

export function slugify(text: string): string {
  return text
    .toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '') // Remove accents
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim();
}

export function generateId(): string {
  return crypto.randomUUID();
}

export function deepClone<T>(obj: T): T {
  return JSON.parse(JSON.stringify(obj));
}

export function isEmpty(value: any): boolean {
  if (value == null) return true;
  if (typeof value === 'string') return value.trim() === '';
  if (Array.isArray(value)) return value.length === 0;
  if (typeof value === 'object') return Object.keys(value).length === 0;
  return false;
}

// Color utilities
export function getStatusColor(status: string): string {
  const statusColors: Record<string, string> = {
    active: "green",
    inactive: "gray",
    pending: "yellow",
    completed: "green",
    cancelled: "red",
    draft: "gray",
    published: "green",
    archived: "red",
    todo: "gray",
    in_progress: "blue",
    done: "green",
    blocked: "red",
    low: "green",
    medium: "yellow",
    high: "orange",
    critical: "red",
    urgent: "red",
  };
  
  return statusColors[status.toLowerCase()] || "gray";
}

export function getPriorityColor(priority: string): string {
  const priorityColors: Record<string, string> = {
    low: "green",
    medium: "yellow",
    high: "orange",
    critical: "red",
    urgent: "red",
  };
  
  return priorityColors[priority.toLowerCase()] || "gray";
}

// Constants
export const DEFAULT_PAGE_SIZE = 10;
export const MAX_PAGE_SIZE = 100;
export const DEFAULT_SORT_DIRECTION = "desc";
export const DEFAULT_SORT_FIELD = "createdAt";

export const COMMON_DATE_FORMATS = {
  SHORT: "dd/MM/yyyy",
  LONG: "dd 'de' MMMM 'de' yyyy",
  WITH_TIME: "dd/MM/yyyy HH:mm",
  ISO: "yyyy-MM-dd",
};

export const COMMON_COLORS = {
  PRIMARY: "#3B82F6",
  SECONDARY: "#6B7280",
  SUCCESS: "#10B981",
  WARNING: "#F59E0B",
  ERROR: "#EF4444",
  INFO: "#06B6D4",
};

// Type guards
export function isCreateInput<T extends BaseEntity>(
  input: any
): input is Omit<T, keyof BaseEntity> {
  return typeof input === 'object' && input !== null && !('id' in input);
}

export function isUpdateInput<T extends BaseEntity>(
  input: any
): input is Partial<Omit<T, keyof BaseEntity>> {
  return typeof input === 'object' && input !== null;
}

export function hasId(obj: any): obj is { id: number } {
  return typeof obj === 'object' && obj !== null && 'id' in obj && isValidId(obj.id);
}

export function hasTimestamps(obj: any): obj is BaseEntity {
  return hasId(obj) && 
         'createdAt' in obj && isValidDate(obj.createdAt) &&
         'updatedAt' in obj && isValidDate(obj.updatedAt);
}

// Error types
export class ValidationError extends Error {
  constructor(
    public field: string,
    message: string
  ) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class NotFoundError extends Error {
  constructor(resource: string, id: number | string) {
    super(`${resource} with id ${id} not found`);
    this.name = 'NotFoundError';
  }
}

export class UnauthorizedError extends Error {
  constructor(action: string) {
    super(`Unauthorized to perform action: ${action}`);
    this.name = 'UnauthorizedError';
  }
}

export class ConflictError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ConflictError';
  }
}
