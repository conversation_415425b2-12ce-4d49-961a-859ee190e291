// Enumeración para roles de usuario
export enum UserRole {
  ADMIN = "admin",
  TEACHER = "teacher",
  STUDENT = "student",
  GUEST = "guest",
}

// Enumeración para estados de usuario
export enum UserStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
  SUSPENDED = "suspended",
  PENDING = "pending",
}

// Interfaz para usuarios
export interface User {
  id: number;
  name: string;
  firstName: string | null;
  lastName: string | null;
  username: string | null;
  email: string;
  password: string;
  role: UserRole | string;
  createdAt: Date;
  updatedAt: Date;
}

// Interfaz extendida con datos relacionados
export interface UserWithDetails extends Omit<User, 'password'> {
  projects?: ProjectMembership[];
  teams?: TeamMembership[];
  stats?: UserStats;
  preferences?: UserPreferences;
}

// Interfaz para membresías de proyecto
export interface ProjectMembership {
  projectId: number;
  projectName: string;
  role: string;
  joinedAt: Date;
}

// Interfaz para membresías de equipo
export interface TeamMembership {
  teamId: number;
  teamName: string;
  role: string;
  joinedAt: Date;
}

// Interfaz para estadísticas de usuario
export interface UserStats {
  totalProjects: number;
  activeProjects: number;
  completedProjects: number;
  totalTasks: number;
  completedTasks: number;
  totalUserStories: number;
  completedUserStories: number;
  totalDeliverables: number;
  submittedDeliverables: number;
  averageScore: number;
  totalEvaluations: number;
}

// Interfaz para preferencias de usuario
export interface UserPreferences {
  theme: "light" | "dark" | "auto";
  language: "es" | "en";
  notifications: {
    email: boolean;
    push: boolean;
    taskAssignments: boolean;
    projectUpdates: boolean;
    evaluations: boolean;
  };
  dashboard: {
    defaultView: "projects" | "tasks" | "calendar";
    itemsPerPage: number;
    showCompletedTasks: boolean;
  };
}

// Interfaz para crear un usuario
export interface CreateUserInput {
  name: string;
  firstName?: string;
  lastName?: string;
  username?: string;
  email: string;
  password: string;
  role?: UserRole | string;
}

// Interfaz para actualizar un usuario
export interface UpdateUserInput {
  name?: string;
  firstName?: string;
  lastName?: string;
  username?: string;
  email?: string;
  role?: UserRole | string;
}

// Interfaz para cambiar contraseña
export interface ChangePasswordInput {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// Interfaz para filtros de búsqueda
export interface UserFilters {
  role?: UserRole | string;
  status?: UserStatus | string;
  projectId?: number;
  teamId?: number;
  search?: string; // Buscar por nombre, email o username
}

// Interfaz para perfil público de usuario
export interface UserProfile {
  id: number;
  name: string;
  firstName: string | null;
  lastName: string | null;
  username: string | null;
  email: string;
  role: string;
  joinedAt: Date;
  stats: {
    projectsCount: number;
    tasksCompleted: number;
    averageScore: number;
  };
}

// Tipos auxiliares
export type UserFormData = Omit<CreateUserInput, 'password'>;
export type UserUpdateData = Partial<UpdateUserInput>;
export type SafeUser = Omit<User, 'password'>;

// Validadores
export function isValidUserRole(role: string): role is UserRole {
  return Object.values(UserRole).includes(role as UserRole);
}

export function isValidUserStatus(status: string): status is UserStatus {
  return Object.values(UserStatus).includes(status as UserStatus);
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function validateUsername(username: string): boolean {
  // Username debe tener 3-20 caracteres, solo letras, números y guiones bajos
  const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
  return usernameRegex.test(username);
}

export function validatePassword(password: string): string[] {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push("La contraseña debe tener al menos 8 caracteres");
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push("La contraseña debe contener al menos una letra mayúscula");
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push("La contraseña debe contener al menos una letra minúscula");
  }
  
  if (!/\d/.test(password)) {
    errors.push("La contraseña debe contener al menos un número");
  }
  
  return errors;
}

// Funciones de utilidad
export function getUserRoleColor(role: UserRole | string): string {
  switch (role) {
    case UserRole.ADMIN:
      return "red";
    case UserRole.TEACHER:
      return "blue";
    case UserRole.STUDENT:
      return "green";
    case UserRole.GUEST:
      return "gray";
    default:
      return "gray";
  }
}

export function getUserRoleIcon(role: UserRole | string): string {
  switch (role) {
    case UserRole.ADMIN:
      return "👑";
    case UserRole.TEACHER:
      return "👨‍🏫";
    case UserRole.STUDENT:
      return "👨‍🎓";
    case UserRole.GUEST:
      return "👤";
    default:
      return "👤";
  }
}

export function getFullName(user: User | UserWithDetails): string {
  if (user.firstName && user.lastName) {
    return `${user.firstName} ${user.lastName}`;
  }
  return user.name;
}

export function getDisplayName(user: User | UserWithDetails): string {
  return user.username || getFullName(user);
}

export function getInitials(user: User | UserWithDetails): string {
  const fullName = getFullName(user);
  return fullName
    .split(' ')
    .map(name => name.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('');
}

export function canEditUser(currentUser: User, targetUser: User): boolean {
  // Admin puede editar a cualquiera
  if (currentUser.role === UserRole.ADMIN) return true;
  
  // Teacher puede editar estudiantes
  if (currentUser.role === UserRole.TEACHER && targetUser.role === UserRole.STUDENT) {
    return true;
  }
  
  // Usuario puede editarse a sí mismo
  return currentUser.id === targetUser.id;
}

export function canDeleteUser(currentUser: User, targetUser: User): boolean {
  // Solo admin puede eliminar usuarios
  if (currentUser.role !== UserRole.ADMIN) return false;
  
  // No puede eliminarse a sí mismo
  return currentUser.id !== targetUser.id;
}

export function hasPermission(user: User, permission: string): boolean {
  const permissions = getUserPermissions(user.role as UserRole);
  return permissions.includes(permission);
}

export function getUserPermissions(role: UserRole): string[] {
  switch (role) {
    case UserRole.ADMIN:
      return [
        "manage_users",
        "manage_projects",
        "manage_teams",
        "view_all_data",
        "generate_reports",
        "manage_system",
      ];
    case UserRole.TEACHER:
      return [
        "create_projects",
        "manage_own_projects",
        "evaluate_students",
        "view_student_data",
        "generate_reports",
      ];
    case UserRole.STUDENT:
      return [
        "view_own_projects",
        "submit_deliverables",
        "view_own_evaluations",
        "participate_in_teams",
      ];
    case UserRole.GUEST:
      return [
        "view_public_data",
      ];
    default:
      return [];
  }
}

// Constantes
export const USER_ROLE_OPTIONS = [
  { value: UserRole.ADMIN, label: "Administrador", icon: "👑", color: "red" },
  { value: UserRole.TEACHER, label: "Profesor", icon: "👨‍🏫", color: "blue" },
  { value: UserRole.STUDENT, label: "Estudiante", icon: "👨‍🎓", color: "green" },
  { value: UserRole.GUEST, label: "Invitado", icon: "👤", color: "gray" },
];

export const USER_STATUS_OPTIONS = [
  { value: UserStatus.ACTIVE, label: "Activo", color: "green" },
  { value: UserStatus.INACTIVE, label: "Inactivo", color: "gray" },
  { value: UserStatus.SUSPENDED, label: "Suspendido", color: "red" },
  { value: UserStatus.PENDING, label: "Pendiente", color: "yellow" },
];

export const DEFAULT_USER_PREFERENCES: UserPreferences = {
  theme: "auto",
  language: "es",
  notifications: {
    email: true,
    push: true,
    taskAssignments: true,
    projectUpdates: true,
    evaluations: true,
  },
  dashboard: {
    defaultView: "projects",
    itemsPerPage: 10,
    showCompletedTasks: false,
  },
};
