import { Handlers } from "$fresh/server.ts";
import {
  getTaskById,
  updateTask,
  deleteTask,
} from "../../../db/services.ts";
import { UpdateTaskInput } from "../../../models/index.ts";

export const handler: Handlers = {
  // GET /api/tasks/[id]
  async GET(_req, ctx) {
    try {
      const id = Number(ctx.params.id);
      
      if (isNaN(id)) {
        return new Response(
          JSON.stringify({ error: "Invalid task ID" }),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }

      const tasks = await getTaskById(id);
      const task = tasks[0];
      
      if (!task) {
        return new Response(
          JSON.stringify({ error: "Task not found" }),
          { status: 404, headers: { "Content-Type": "application/json" } }
        );
      }
      
      return new Response(
        JSON.stringify({ success: true, data: task }),
        { status: 200, headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("Error fetching task:", error);
      return new Response(
        JSON.stringify({ error: "Failed to fetch task" }),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  },

  // PUT /api/tasks/[id]
  async PUT(req, ctx) {
    try {
      const id = Number(ctx.params.id);
      
      if (isNaN(id)) {
        return new Response(
          JSON.stringify({ error: "Invalid task ID" }),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }

      const body = await req.json();
      
      const updateData: UpdateTaskInput = {};
      
      if (body.title !== undefined) updateData.title = body.title;
      if (body.description !== undefined) updateData.description = body.description;
      if (body.userStoryId !== undefined) updateData.userStoryId = body.userStoryId ? Number(body.userStoryId) : null;
      if (body.sprintId !== undefined) updateData.sprintId = body.sprintId ? Number(body.sprintId) : null;
      if (body.assigneeId !== undefined) updateData.assigneeId = body.assigneeId ? Number(body.assigneeId) : null;
      if (body.assignedTo !== undefined) updateData.assignedTo = body.assignedTo;
      if (body.status !== undefined) updateData.status = body.status;
      if (body.priority !== undefined) updateData.priority = body.priority;
      if (body.storyPoints !== undefined) updateData.storyPoints = body.storyPoints ? Number(body.storyPoints) : null;
      if (body.estimatedHours !== undefined) updateData.estimatedHours = body.estimatedHours ? Number(body.estimatedHours) : null;
      if (body.spentHours !== undefined) updateData.spentHours = body.spentHours ? Number(body.spentHours) : null;

      const updatedTasks = await updateTask(id, updateData);
      const updatedTask = updatedTasks[0];
      
      if (!updatedTask) {
        return new Response(
          JSON.stringify({ error: "Task not found" }),
          { status: 404, headers: { "Content-Type": "application/json" } }
        );
      }
      
      return new Response(
        JSON.stringify({ success: true, data: updatedTask }),
        { status: 200, headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("Error updating task:", error);
      return new Response(
        JSON.stringify({ error: "Failed to update task" }),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  },

  // DELETE /api/tasks/[id]
  async DELETE(_req, ctx) {
    try {
      const id = Number(ctx.params.id);
      
      if (isNaN(id)) {
        return new Response(
          JSON.stringify({ error: "Invalid task ID" }),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }

      const deletedTasks = await deleteTask(id);
      const deletedTask = deletedTasks[0];
      
      if (!deletedTask) {
        return new Response(
          JSON.stringify({ error: "Task not found" }),
          { status: 404, headers: { "Content-Type": "application/json" } }
        );
      }
      
      return new Response(
        JSON.stringify({ success: true, data: deletedTask }),
        { status: 200, headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("Error deleting task:", error);
      return new Response(
        JSON.stringify({ error: "Failed to delete task" }),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  },
};
