import { Handlers } from "$fresh/server.ts";
import {
  createTask,
  getTasksBySprintId,
  getTasksByUserStoryId,
  updateTask,
  deleteTask,
} from "../../db/services.ts";
import { CreateTaskInput } from "../../models/index.ts";

export const handler: Handlers = {
  // GET /api/tasks?sprintId=1 or ?userStoryId=1
  async GET(req) {
    try {
      const url = new URL(req.url);
      const sprintId = url.searchParams.get("sprintId");
      const userStoryId = url.searchParams.get("userStoryId");
      
      let tasks;
      
      if (sprintId) {
        tasks = await getTasksBySprintId(Number(sprintId));
      } else if (userStoryId) {
        tasks = await getTasksByUserStoryId(Number(userStoryId));
      } else {
        return new Response(
          JSON.stringify({ error: "sprintId or userStoryId is required" }),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }
      
      return new Response(
        JSON.stringify({ success: true, data: tasks }),
        { status: 200, headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("Error fetching tasks:", error);
      return new Response(
        JSON.stringify({ error: "Failed to fetch tasks" }),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  },

  // POST /api/tasks
  async POST(req) {
    try {
      const body = await req.json();
      
      // Validar datos requeridos
      if (!body.title) {
        return new Response(
          JSON.stringify({ error: "Title is required" }),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }

      const taskData: CreateTaskInput = {
        title: body.title,
        description: body.description || null,
        userStoryId: body.userStoryId ? Number(body.userStoryId) : null,
        sprintId: body.sprintId ? Number(body.sprintId) : null,
        assigneeId: body.assigneeId ? Number(body.assigneeId) : null,
        assignedTo: body.assignedTo || null,
        status: body.status || "todo",
        priority: body.priority || "medium",
        storyPoints: body.storyPoints ? Number(body.storyPoints) : null,
        estimatedHours: body.estimatedHours ? Number(body.estimatedHours) : null,
        createdBy: body.createdBy ? Number(body.createdBy) : null,
      };

      const [newTask] = await createTask(taskData);
      
      return new Response(
        JSON.stringify({ success: true, data: newTask }),
        { status: 201, headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("Error creating task:", error);
      return new Response(
        JSON.stringify({ error: "Failed to create task" }),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  },
};
