import { Handlers } from "$fresh/server.ts";
import {
  getUserStoryById,
  updateUserStory,
  deleteUserStory,
} from "../../../db/services.ts";
import { UpdateUserStoryInput } from "../../../models/index.ts";

export const handler: Handlers = {
  // GET /api/user-stories/[id]
  async GET(_req, ctx) {
    try {
      const id = Number(ctx.params.id);
      
      if (isNaN(id)) {
        return new Response(
          JSON.stringify({ error: "Invalid user story ID" }),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }

      const userStories = await getUserStoryById(id);
      const userStory = userStories[0];
      
      if (!userStory) {
        return new Response(
          JSON.stringify({ error: "User story not found" }),
          { status: 404, headers: { "Content-Type": "application/json" } }
        );
      }
      
      return new Response(
        JSON.stringify({ success: true, data: userStory }),
        { status: 200, headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("Error fetching user story:", error);
      return new Response(
        JSON.stringify({ error: "Failed to fetch user story" }),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  },

  // PUT /api/user-stories/[id]
  async PUT(req, ctx) {
    try {
      const id = Number(ctx.params.id);
      
      if (isNaN(id)) {
        return new Response(
          JSON.stringify({ error: "Invalid user story ID" }),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }

      const body = await req.json();
      
      const updateData: UpdateUserStoryInput = {};
      
      if (body.title !== undefined) updateData.title = body.title;
      if (body.description !== undefined) updateData.description = body.description;
      if (body.acceptanceCriteria !== undefined) updateData.acceptanceCriteria = body.acceptanceCriteria;
      if (body.priority !== undefined) updateData.priority = body.priority;
      if (body.points !== undefined) updateData.points = body.points ? Number(body.points) : null;
      if (body.sprintId !== undefined) updateData.sprintId = body.sprintId ? Number(body.sprintId) : null;
      if (body.status !== undefined) updateData.status = body.status;

      const updatedUserStories = await updateUserStory(id, updateData);
      const updatedUserStory = updatedUserStories[0];
      
      if (!updatedUserStory) {
        return new Response(
          JSON.stringify({ error: "User story not found" }),
          { status: 404, headers: { "Content-Type": "application/json" } }
        );
      }
      
      return new Response(
        JSON.stringify({ success: true, data: updatedUserStory }),
        { status: 200, headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("Error updating user story:", error);
      return new Response(
        JSON.stringify({ error: "Failed to update user story" }),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  },

  // DELETE /api/user-stories/[id]
  async DELETE(_req, ctx) {
    try {
      const id = Number(ctx.params.id);
      
      if (isNaN(id)) {
        return new Response(
          JSON.stringify({ error: "Invalid user story ID" }),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }

      const deletedUserStories = await deleteUserStory(id);
      const deletedUserStory = deletedUserStories[0];
      
      if (!deletedUserStory) {
        return new Response(
          JSON.stringify({ error: "User story not found" }),
          { status: 404, headers: { "Content-Type": "application/json" } }
        );
      }
      
      return new Response(
        JSON.stringify({ success: true, data: deletedUserStory }),
        { status: 200, headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("Error deleting user story:", error);
      return new Response(
        JSON.stringify({ error: "Failed to delete user story" }),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  },
};
