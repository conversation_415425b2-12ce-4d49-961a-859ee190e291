import { Handlers } from "$fresh/server.ts";
import {
  createUserStory,
  getUserStoriesByProjectId,
  updateUserStory,
  deleteUserStory,
  getUserStoryById,
} from "../../db/services.ts";
import { CreateUserStoryInput, UpdateUserStoryInput } from "../../models/index.ts";

export const handler: Handlers = {
  // GET /api/user-stories?projectId=1
  async GET(req) {
    try {
      const url = new URL(req.url);
      const projectId = url.searchParams.get("projectId");
      
      if (!projectId) {
        return new Response(
          JSON.stringify({ error: "projectId is required" }),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }

      const userStories = await getUserStoriesByProjectId(Number(projectId));
      
      return new Response(
        JSON.stringify({ success: true, data: userStories }),
        { status: 200, headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("Error fetching user stories:", error);
      return new Response(
        JSON.stringify({ error: "Failed to fetch user stories" }),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  },

  // POST /api/user-stories
  async POST(req) {
    try {
      const body = await req.json();
      
      // Validar datos requeridos
      if (!body.title || !body.description || !body.acceptanceCriteria || !body.projectId) {
        return new Response(
          JSON.stringify({ error: "Missing required fields" }),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }

      const userStoryData: CreateUserStoryInput = {
        title: body.title,
        description: body.description,
        acceptanceCriteria: body.acceptanceCriteria,
        priority: body.priority || "medium",
        points: body.points ? Number(body.points) : null,
        projectId: Number(body.projectId),
        sprintId: body.sprintId ? Number(body.sprintId) : null,
        status: body.status || "backlog",
        createdBy: body.createdBy ? Number(body.createdBy) : null,
      };

      const [newUserStory] = await createUserStory(userStoryData);
      
      return new Response(
        JSON.stringify({ success: true, data: newUserStory }),
        { status: 201, headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("Error creating user story:", error);
      return new Response(
        JSON.stringify({ error: "Failed to create user story" }),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  },
};
