import { Handlers, PageProps } from "$fresh/server.ts";
import { getAllProjects } from "../db/services.ts";
import CreateUserStoryIsland from "../islands/CreateUserStoryIsland.tsx";
import UserStoriesListIsland from "../islands/UserStoriesListIsland.tsx";
import type { Project } from "../models/project.ts";

interface TestComponentsData {
  projects: Project[];
}

export const handler: Handlers<TestComponentsData> = {
  async GET(_req, ctx) {
    try {
      const projects = await getAllProjects();

      return ctx.render({
        projects,
      });
    } catch (error) {
      console.error("Error loading test components data:", error);
      return ctx.render({
        projects: [],
      });
    }
  },
};

export default function TestComponentsPage({ data }: PageProps<TestComponentsData>) {
  const { projects } = data;

  return (
    <div class="min-h-screen bg-gray-50">
      <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="px-4 py-6 sm:px-0">
          <div class="border-4 border-dashed border-gray-200 rounded-lg p-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-8">
              Prueba de Componentes Adaptados
            </h1>
            
            <div class="space-y-8">
              {/* Sección User Stories */}
              <div class="bg-white shadow rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">
                  User Stories
                </h2>
                <p class="text-gray-600 mb-4">
                  Prueba el componente de creación de historias de usuario adaptado para PostgreSQL.
                </p>
                
                <div class="mb-4">
                  <CreateUserStoryIsland 
                    projects={projects}
                  />
                </div>
                
                <div class="mt-6">
                  <h3 class="text-lg font-medium text-gray-900 mb-2">
                    Proyectos disponibles:
                  </h3>
                  <ul class="list-disc list-inside text-gray-600">
                    {projects.map((project) => (
                      <li key={project.id}>
                        {project.name} (ID: {project.id})
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Lista de User Stories existentes */}
                {projects.length > 0 && (
                  <div class="mt-6">
                    <UserStoriesListIsland projectId={projects[0].id} />
                  </div>
                )}
              </div>

              {/* Sección Tasks */}
              <div class="bg-white shadow rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">
                  Tasks
                </h2>
                <p class="text-gray-600 mb-4">
                  Para probar el componente de tareas, primero necesitas crear una User Story.
                </p>
                
                <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                  <div class="flex">
                    <div class="flex-shrink-0">
                      <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                      </svg>
                    </div>
                    <div class="ml-3">
                      <h3 class="text-sm font-medium text-blue-800">
                        Instrucciones
                      </h3>
                      <div class="mt-2 text-sm text-blue-700">
                        <ol class="list-decimal list-inside space-y-1">
                          <li>Crea una User Story usando el botón de arriba</li>
                          <li>Anota el ID de la User Story creada</li>
                          <li>Ve a la consola del navegador para ver los datos</li>
                          <li>Usa las APIs para crear tareas asociadas</li>
                        </ol>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Sección APIs */}
              <div class="bg-white shadow rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">
                  APIs Disponibles
                </h2>
                <p class="text-gray-600 mb-4">
                  Las siguientes APIs están disponibles para probar:
                </p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div class="border border-gray-200 rounded-lg p-4">
                    <h3 class="font-medium text-gray-900 mb-2">User Stories</h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                      <li><code class="bg-gray-100 px-1 rounded">GET /api/user-stories?projectId=1</code></li>
                      <li><code class="bg-gray-100 px-1 rounded">POST /api/user-stories</code></li>
                      <li><code class="bg-gray-100 px-1 rounded">GET /api/user-stories/[id]</code></li>
                      <li><code class="bg-gray-100 px-1 rounded">PUT /api/user-stories/[id]</code></li>
                      <li><code class="bg-gray-100 px-1 rounded">DELETE /api/user-stories/[id]</code></li>
                    </ul>
                  </div>
                  
                  <div class="border border-gray-200 rounded-lg p-4">
                    <h3 class="font-medium text-gray-900 mb-2">Tasks</h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                      <li><code class="bg-gray-100 px-1 rounded">GET /api/tasks?userStoryId=1</code></li>
                      <li><code class="bg-gray-100 px-1 rounded">POST /api/tasks</code></li>
                      <li><code class="bg-gray-100 px-1 rounded">GET /api/tasks/[id]</code></li>
                      <li><code class="bg-gray-100 px-1 rounded">PUT /api/tasks/[id]</code></li>
                      <li><code class="bg-gray-100 px-1 rounded">DELETE /api/tasks/[id]</code></li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Sección Testing */}
              <div class="bg-white shadow rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">
                  Scripts de Prueba
                </h2>
                <p class="text-gray-600 mb-4">
                  Ejecuta estos comandos en la terminal para probar los servicios:
                </p>
                
                <div class="bg-gray-900 text-gray-100 p-4 rounded-lg font-mono text-sm">
                  <div class="space-y-2">
                    <div># Probar servicios de base de datos</div>
                    <div class="text-green-400">deno task test-new-services</div>
                    <div class="mt-4"># Probar modelos TypeScript</div>
                    <div class="text-green-400">deno task test-models</div>
                    <div class="mt-4"># Probar APIs (requiere servidor corriendo)</div>
                    <div class="text-green-400">deno task test-api</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
