{"lock": false, "tasks": {"init": "deno run -A scripts/init-project.ts", "check": "deno fmt --check && deno lint && deno check **/*.ts && deno check **/*.tsx", "cli": "echo \"import '\\$fresh/src/dev/cli.ts'\" | deno run --unstable -A -", "manifest": "deno task cli manifest $(pwd)", "start": "deno run -A --watch=static/,routes/ dev.ts", "build": "deno run -A dev.ts build", "preview": "deno run -A main.ts", "update": "deno run -A -r https://fresh.deno.dev/update .", "migrate": "deno run -A scripts/migrate.ts", "test-db": "deno run -A scripts/test-db.ts", "verify": "deno run -A scripts/verify-setup.ts", "seed-projects": "deno run -A scripts/seed-projects.ts", "test-members": "deno run -A scripts/test-project-members.ts", "seed-users": "deno run -A scripts/seed-users.ts", "test-new-services": "deno run -A scripts/test-new-services.ts", "test-models": "deno run -A scripts/test-models.ts", "test-api": "deno run -A scripts/test-api-routes.ts"}, "lint": {"rules": {"tags": ["fresh", "recommended"]}}, "exclude": ["**/_fresh/*"], "imports": {"$fresh/": "https://deno.land/x/fresh@1.7.3/", "@biomejs/biome": "npm:@biomejs/biome@^1.9.4", "@types/pg": "npm:@types/pg@^8.11.13", "bcrypt": "https://deno.land/x/bcrypt@v0.4.1/mod.ts", "drizzle-kit": "npm:drizzle-kit@^0.31.0", "drizzle-orm": "npm:drizzle-orm@^0.42.0", "pg": "npm:pg@^8.15.5", "preact": "https://esm.sh/preact@10.22.0", "preact/": "https://esm.sh/preact@10.22.0/", "@preact/signals": "https://esm.sh/*@preact/signals@1.2.2", "@preact/signals-core": "https://esm.sh/*@preact/signals-core@1.5.1", "tailwindcss": "npm:tailwindcss@3.4.1", "tailwindcss/": "npm:/tailwindcss@3.4.1/", "tailwindcss/plugin": "npm:/tailwindcss@3.4.1/plugin.js", "$std/": "https://deno.land/std@0.216.0/"}, "compilerOptions": {"jsx": "react-jsx", "jsxImportSource": "preact"}, "nodeModulesDir": "auto"}