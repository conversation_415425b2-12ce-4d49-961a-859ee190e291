import { useState, useEffect } from "preact/hooks";
import { 
  UserStory, 
  getUserStoryStatusColor, 
  getUserStoryPriorityColor 
} from "../models/index.ts";

interface UserStoriesListIslandProps {
  projectId: number;
}

export default function UserStoriesListIsland({ projectId }: UserStoriesListIslandProps) {
  const [userStories, setUserStories] = useState<UserStory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadUserStories = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/user-stories?projectId=${projectId}`);
      
      if (!response.ok) {
        throw new Error("Error al cargar las historias de usuario");
      }
      
      const data = await response.json();
      setUserStories(data.data || []);
    } catch (err) {
      console.error("Error loading user stories:", err);
      setError(err instanceof Error ? err.message : "Error desconocido");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadUserStories();
  }, [projectId]);

  const handleRefresh = () => {
    loadUserStories();
  };

  const getStatusBadgeClass = (status: string) => {
    const color = getUserStoryStatusColor(status);
    const colorClasses = {
      gray: "bg-gray-100 text-gray-800",
      blue: "bg-blue-100 text-blue-800",
      yellow: "bg-yellow-100 text-yellow-800",
      orange: "bg-orange-100 text-orange-800",
      green: "bg-green-100 text-green-800",
      red: "bg-red-100 text-red-800",
    };
    return `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClasses[color] || colorClasses.gray}`;
  };

  const getPriorityBadgeClass = (priority: string) => {
    const color = getUserStoryPriorityColor(priority);
    const colorClasses = {
      gray: "bg-gray-100 text-gray-800",
      blue: "bg-blue-100 text-blue-800",
      yellow: "bg-yellow-100 text-yellow-800",
      orange: "bg-orange-100 text-orange-800",
      green: "bg-green-100 text-green-800",
      red: "bg-red-100 text-red-800",
    };
    return `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClasses[color] || colorClasses.gray}`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Cargando historias de usuario...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error</h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{error}</p>
            </div>
            <div className="mt-4">
              <button
                onClick={handleRefresh}
                className="bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200"
              >
                Reintentar
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">
          Historias de Usuario ({userStories.length})
        </h3>
        <button
          onClick={handleRefresh}
          className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          Actualizar
        </button>
      </div>

      {userStories.length === 0 ? (
        <div className="text-center py-8">
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">No hay historias de usuario</h3>
          <p className="mt-1 text-sm text-gray-500">
            Comienza creando una nueva historia de usuario para este proyecto.
          </p>
        </div>
      ) : (
        <div className="grid gap-4">
          {userStories.map((userStory) => (
            <div key={userStory.id} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <h4 className="text-sm font-medium text-gray-900">
                      #{userStory.id} - {userStory.title}
                    </h4>
                    <span className={getStatusBadgeClass(userStory.status)}>
                      {userStory.status}
                    </span>
                    <span className={getPriorityBadgeClass(userStory.priority)}>
                      {userStory.priority}
                    </span>
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-2">
                    {userStory.description}
                  </p>
                  
                  <div className="text-xs text-gray-500">
                    <span>Puntos: {userStory.points || "No asignados"}</span>
                    {userStory.sprintId && (
                      <span className="ml-4">Sprint: {userStory.sprintId}</span>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center space-x-2 ml-4">
                  <button
                    onClick={() => {
                      console.log("User Story details:", userStory);
                      alert(`Detalles en consola para User Story #${userStory.id}`);
                    }}
                    className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                  >
                    Ver detalles
                  </button>
                </div>
              </div>
              
              {userStory.acceptanceCriteria && (
                <div className="mt-3 pt-3 border-t border-gray-100">
                  <h5 className="text-xs font-medium text-gray-700 mb-1">Criterios de Aceptación:</h5>
                  <div className="text-xs text-gray-600 whitespace-pre-line">
                    {userStory.acceptanceCriteria}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
