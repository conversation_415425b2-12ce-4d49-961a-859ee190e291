{"id": "ab03ff73-fd6b-41a6-b8ee-c1183ecc3d2b", "prevId": "8abfba60-f7db-4d99-8275-5ba1d7af5ca1", "version": "7", "dialect": "postgresql", "tables": {"public.backlog_items": {"name": "backlog_items", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "project_id": {"name": "project_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_story_id": {"name": "user_story_id", "type": "integer", "primaryKey": false, "notNull": false}, "priority": {"name": "priority", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'backlog'"}, "estimated_effort": {"name": "estimated_effort", "type": "integer", "primaryKey": false, "notNull": false}, "business_value": {"name": "business_value", "type": "integer", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"backlog_items_project_id_projects_id_fk": {"name": "backlog_items_project_id_projects_id_fk", "tableFrom": "backlog_items", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "backlog_items_user_story_id_user_stories_id_fk": {"name": "backlog_items_user_story_id_user_stories_id_fk", "tableFrom": "backlog_items", "tableTo": "user_stories", "columnsFrom": ["user_story_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "backlog_items_created_by_users_id_fk": {"name": "backlog_items_created_by_users_id_fk", "tableFrom": "backlog_items", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.comments": {"name": "comments", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "task_id": {"name": "task_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"comments_task_id_tasks_id_fk": {"name": "comments_task_id_tasks_id_fk", "tableFrom": "comments", "tableTo": "tasks", "columnsFrom": ["task_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "comments_user_id_users_id_fk": {"name": "comments_user_id_users_id_fk", "tableFrom": "comments", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.deliverables": {"name": "deliverables", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "project_id": {"name": "project_id", "type": "integer", "primaryKey": false, "notNull": true}, "sprint_id": {"name": "sprint_id", "type": "integer", "primaryKey": false, "notNull": false}, "user_story_id": {"name": "user_story_id", "type": "integer", "primaryKey": false, "notNull": false}, "due_date": {"name": "due_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "submitted_at": {"name": "submitted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'pending'"}, "submitted_by": {"name": "submitted_by", "type": "integer", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"deliverables_project_id_projects_id_fk": {"name": "deliverables_project_id_projects_id_fk", "tableFrom": "deliverables", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "deliverables_sprint_id_sprints_id_fk": {"name": "deliverables_sprint_id_sprints_id_fk", "tableFrom": "deliverables", "tableTo": "sprints", "columnsFrom": ["sprint_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "deliverables_user_story_id_user_stories_id_fk": {"name": "deliverables_user_story_id_user_stories_id_fk", "tableFrom": "deliverables", "tableTo": "user_stories", "columnsFrom": ["user_story_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "deliverables_submitted_by_users_id_fk": {"name": "deliverables_submitted_by_users_id_fk", "tableFrom": "deliverables", "tableTo": "users", "columnsFrom": ["submitted_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "deliverables_created_by_users_id_fk": {"name": "deliverables_created_by_users_id_fk", "tableFrom": "deliverables", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.evaluations": {"name": "evaluations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "deliverable_id": {"name": "deliverable_id", "type": "integer", "primaryKey": false, "notNull": false}, "project_id": {"name": "project_id", "type": "integer", "primaryKey": false, "notNull": false}, "team_id": {"name": "team_id", "type": "integer", "primaryKey": false, "notNull": false}, "student_id": {"name": "student_id", "type": "integer", "primaryKey": false, "notNull": false}, "evaluator_id": {"name": "evaluator_id", "type": "integer", "primaryKey": false, "notNull": true}, "rubric_id": {"name": "rubric_id", "type": "integer", "primaryKey": false, "notNull": false}, "criteria_evaluations": {"name": "criteria_evaluations", "type": "json", "primaryKey": false, "notNull": false}, "total_score": {"name": "total_score", "type": "integer", "primaryKey": false, "notNull": true}, "max_possible_score": {"name": "max_possible_score", "type": "integer", "primaryKey": false, "notNull": true}, "overall_feedback": {"name": "overall_feedback", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'draft'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"evaluations_deliverable_id_deliverables_id_fk": {"name": "evaluations_deliverable_id_deliverables_id_fk", "tableFrom": "evaluations", "tableTo": "deliverables", "columnsFrom": ["deliverable_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "evaluations_project_id_projects_id_fk": {"name": "evaluations_project_id_projects_id_fk", "tableFrom": "evaluations", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "evaluations_team_id_teams_id_fk": {"name": "evaluations_team_id_teams_id_fk", "tableFrom": "evaluations", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "evaluations_student_id_users_id_fk": {"name": "evaluations_student_id_users_id_fk", "tableFrom": "evaluations", "tableTo": "users", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "evaluations_evaluator_id_users_id_fk": {"name": "evaluations_evaluator_id_users_id_fk", "tableFrom": "evaluations", "tableTo": "users", "columnsFrom": ["evaluator_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "evaluations_rubric_id_rubrics_id_fk": {"name": "evaluations_rubric_id_rubrics_id_fk", "tableFrom": "evaluations", "tableTo": "rubrics", "columnsFrom": ["rubric_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.project_members": {"name": "project_members", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "project_id": {"name": "project_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'team_member'"}, "joined_at": {"name": "joined_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"project_members_project_id_projects_id_fk": {"name": "project_members_project_id_projects_id_fk", "tableFrom": "project_members", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "project_members_user_id_users_id_fk": {"name": "project_members_user_id_users_id_fk", "tableFrom": "project_members", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.projects": {"name": "projects", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "owner_id": {"name": "owner_id", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"projects_owner_id_users_id_fk": {"name": "projects_owner_id_users_id_fk", "tableFrom": "projects", "tableTo": "users", "columnsFrom": ["owner_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.reports": {"name": "reports", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "project_id": {"name": "project_id", "type": "integer", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "data": {"name": "data", "type": "json", "primaryKey": false, "notNull": false}, "generated_by": {"name": "generated_by", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"reports_project_id_projects_id_fk": {"name": "reports_project_id_projects_id_fk", "tableFrom": "reports", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "reports_generated_by_users_id_fk": {"name": "reports_generated_by_users_id_fk", "tableFrom": "reports", "tableTo": "users", "columnsFrom": ["generated_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.rubrics": {"name": "rubrics", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "project_id": {"name": "project_id", "type": "integer", "primaryKey": false, "notNull": false}, "is_template": {"name": "is_template", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'draft'"}, "criteria": {"name": "criteria", "type": "json", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"rubrics_project_id_projects_id_fk": {"name": "rubrics_project_id_projects_id_fk", "tableFrom": "rubrics", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "rubrics_created_by_users_id_fk": {"name": "rubrics_created_by_users_id_fk", "tableFrom": "rubrics", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sprints": {"name": "sprints", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "goal": {"name": "goal", "type": "text", "primaryKey": false, "notNull": false}, "project_id": {"name": "project_id", "type": "integer", "primaryKey": false, "notNull": true}, "start_date": {"name": "start_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "end_date": {"name": "end_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'planned'"}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"sprints_project_id_projects_id_fk": {"name": "sprints_project_id_projects_id_fk", "tableFrom": "sprints", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "sprints_created_by_users_id_fk": {"name": "sprints_created_by_users_id_fk", "tableFrom": "sprints", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tasks": {"name": "tasks", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "user_story_id": {"name": "user_story_id", "type": "integer", "primaryKey": false, "notNull": false}, "sprint_id": {"name": "sprint_id", "type": "integer", "primaryKey": false, "notNull": false}, "assignee_id": {"name": "assignee_id", "type": "integer", "primaryKey": false, "notNull": false}, "assigned_to": {"name": "assigned_to", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'todo'"}, "priority": {"name": "priority", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'medium'"}, "story_points": {"name": "story_points", "type": "integer", "primaryKey": false, "notNull": false}, "estimated_hours": {"name": "estimated_hours", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "spent_hours": {"name": "spent_hours", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"tasks_user_story_id_user_stories_id_fk": {"name": "tasks_user_story_id_user_stories_id_fk", "tableFrom": "tasks", "tableTo": "user_stories", "columnsFrom": ["user_story_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "tasks_sprint_id_sprints_id_fk": {"name": "tasks_sprint_id_sprints_id_fk", "tableFrom": "tasks", "tableTo": "sprints", "columnsFrom": ["sprint_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "tasks_assignee_id_users_id_fk": {"name": "tasks_assignee_id_users_id_fk", "tableFrom": "tasks", "tableTo": "users", "columnsFrom": ["assignee_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "tasks_created_by_users_id_fk": {"name": "tasks_created_by_users_id_fk", "tableFrom": "tasks", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.team_members": {"name": "team_members", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "team_id": {"name": "team_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'team_member'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"team_members_team_id_teams_id_fk": {"name": "team_members_team_id_teams_id_fk", "tableFrom": "team_members", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "team_members_user_id_users_id_fk": {"name": "team_members_user_id_users_id_fk", "tableFrom": "team_members", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.teams": {"name": "teams", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "project_id": {"name": "project_id", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"teams_project_id_projects_id_fk": {"name": "teams_project_id_projects_id_fk", "tableFrom": "teams", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_stories": {"name": "user_stories", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "acceptance_criteria": {"name": "acceptance_criteria", "type": "text", "primaryKey": false, "notNull": true}, "priority": {"name": "priority", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'medium'"}, "points": {"name": "points", "type": "integer", "primaryKey": false, "notNull": false}, "project_id": {"name": "project_id", "type": "integer", "primaryKey": false, "notNull": true}, "sprint_id": {"name": "sprint_id", "type": "integer", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'backlog'"}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_stories_project_id_projects_id_fk": {"name": "user_stories_project_id_projects_id_fk", "tableFrom": "user_stories", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_stories_sprint_id_sprints_id_fk": {"name": "user_stories_sprint_id_sprints_id_fk", "tableFrom": "user_stories", "tableTo": "sprints", "columnsFrom": ["sprint_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_stories_created_by_users_id_fk": {"name": "user_stories_created_by_users_id_fk", "tableFrom": "user_stories", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'student'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_username_unique": {"name": "users_username_unique", "nullsNotDistinct": false, "columns": ["username"]}, "users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}