import {
  // User models
  UserRole,
  UserStatus,
  User,
  UserWithDetails,
  CreateUserInput,
  UpdateUserInput,
  validateEmail,
  validateUsername,
  validatePassword,
  getUserRoleColor,
  getFullName,
  
  // Project models
  ProjectRole,
  ProjectStatus,
  Project,
  ProjectMember,
  
  // User Story models
  UserStoryPriority,
  UserStoryStatus,
  UserStory,
  CreateUserStoryInput,
  isValidUserStoryPriority,
  getUserStoryStatusColor,
  
  // Task models
  TaskStatus,
  TaskPriority,
  Task,
  CreateTaskInput,
  getTaskStatusColor,
  calculateTaskProgress,
  
  // Sprint models
  SprintStatus,
  Sprint,
  CreateSprintInput,
  getSprintStatusColor,
  getSprintDuration,
  
  // Rubric models
  RubricStatus,
  Rubric,
  RubricCriterion,
  CreateRubricInput,
  validateRubricCriteria,
  calculateTotalMaxPoints,
  
  // Deliverable models
  DeliverableStatus,
  Deliverable,
  CreateDeliverableInput,
  getDeliverableStatusColor,
  isDeliverableOverdue,
  
  // Evaluation models
  EvaluationStatus,
  Evaluation,
  CreateEvaluationInput,
  calculatePercentage,
  getLetterGrade,
  
  // Report models
  ReportType,
  Report,
  CreateReportInput,
  getReportTypeIcon,
  
  // Backlog Item models
  BacklogItemStatus,
  BacklogItem,
  CreateBacklogItemInput,
  calculatePriorityScore,
  sortBacklogByPriority,
  
  // Common utilities
  formatDate,
  formatRelativeTime,
  truncateText,
  slugify,
  isValidId,
  getStatusColor,
  
} from "../models/index.ts";

/**
 * Script para probar que todos los modelos TypeScript funcionan correctamente
 */
function testModels() {
  console.log("🧪 Probando modelos TypeScript...");
  console.log("=" .repeat(60));
  
  try {
    // Test 1: User models
    console.log("1️⃣ Probando User models...");
    
    const testUser: User = {
      id: 1,
      name: "Juan Pérez",
      firstName: "Juan",
      lastName: "Pérez",
      username: "jperez",
      email: "<EMAIL>",
      password: "hashedpassword",
      role: UserRole.STUDENT,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    console.log(`   ✅ Usuario creado: ${getFullName(testUser)}`);
    console.log(`   ✅ Color del rol: ${getUserRoleColor(testUser.role)}`);
    console.log(`   ✅ Email válido: ${validateEmail(testUser.email)}`);
    console.log(`   ✅ Username válido: ${validateUsername(testUser.username!)}`);
    
    // Test 2: User Story models
    console.log("\n2️⃣ Probando User Story models...");
    
    const testUserStory: UserStory = {
      id: 1,
      title: "Como usuario quiero login",
      description: "Necesito poder autenticarme",
      acceptanceCriteria: "- Formulario de login\n- Validación",
      priority: UserStoryPriority.HIGH,
      points: 5,
      projectId: 1,
      sprintId: 1,
      status: UserStoryStatus.IN_PROGRESS,
      createdBy: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    console.log(`   ✅ User Story creada: ${testUserStory.title}`);
    console.log(`   ✅ Prioridad válida: ${isValidUserStoryPriority(testUserStory.priority)}`);
    console.log(`   ✅ Color del estado: ${getUserStoryStatusColor(testUserStory.status)}`);
    
    // Test 3: Task models
    console.log("\n3️⃣ Probando Task models...");
    
    const testTask: Task = {
      id: 1,
      title: "Implementar formulario de login",
      description: "Crear el componente de login",
      userStoryId: 1,
      sprintId: 1,
      assigneeId: 1,
      assignedTo: "Juan Pérez",
      status: TaskStatus.IN_PROGRESS,
      priority: TaskPriority.HIGH,
      storyPoints: 3,
      estimatedHours: 8,
      spentHours: 4,
      createdBy: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    console.log(`   ✅ Task creada: ${testTask.title}`);
    console.log(`   ✅ Color del estado: ${getTaskStatusColor(testTask.status)}`);
    console.log(`   ✅ Progreso: ${calculateTaskProgress(testTask)}%`);
    
    // Test 4: Sprint models
    console.log("\n4️⃣ Probando Sprint models...");
    
    const testSprint: Sprint = {
      id: 1,
      name: "Sprint 1",
      description: "Primer sprint del proyecto",
      goal: "Implementar autenticación básica",
      projectId: 1,
      startDate: new Date(),
      endDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
      status: SprintStatus.ACTIVE,
      createdBy: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    console.log(`   ✅ Sprint creado: ${testSprint.name}`);
    console.log(`   ✅ Color del estado: ${getSprintStatusColor(testSprint.status)}`);
    console.log(`   ✅ Duración: ${getSprintDuration(testSprint)} días`);
    
    // Test 5: Rubric models
    console.log("\n5️⃣ Probando Rubric models...");
    
    const testRubricCriteria = {
      criteria: [
        {
          id: "1",
          name: "Funcionalidad",
          description: "El código funciona correctamente",
          maxPoints: 10,
          levels: [
            { id: "1", description: "Excelente", pointValue: 10 },
            { id: "2", description: "Bueno", pointValue: 7 },
          ],
        },
      ],
    };
    
    const testRubric: Rubric = {
      id: 1,
      name: "Rúbrica de Código",
      description: "Para evaluar calidad del código",
      projectId: 1,
      isTemplate: false,
      status: RubricStatus.ACTIVE,
      criteria: testRubricCriteria,
      createdBy: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    console.log(`   ✅ Rúbrica creada: ${testRubric.name}`);
    console.log(`   ✅ Criterios válidos: ${validateRubricCriteria(testRubricCriteria).length === 0}`);
    console.log(`   ✅ Puntos totales: ${calculateTotalMaxPoints(testRubricCriteria)}`);
    
    // Test 6: Deliverable models
    console.log("\n6️⃣ Probando Deliverable models...");
    
    const testDeliverable: Deliverable = {
      id: 1,
      title: "Entregable 1",
      description: "Primer entregable del proyecto",
      projectId: 1,
      sprintId: 1,
      userStoryId: 1,
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      submittedAt: null,
      status: DeliverableStatus.PENDING,
      submittedBy: null,
      createdBy: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    console.log(`   ✅ Deliverable creado: ${testDeliverable.title}`);
    console.log(`   ✅ Color del estado: ${getDeliverableStatusColor(testDeliverable.status)}`);
    console.log(`   ✅ Está vencido: ${isDeliverableOverdue(testDeliverable)}`);
    
    // Test 7: Evaluation models
    console.log("\n7️⃣ Probando Evaluation models...");
    
    const testEvaluation: Evaluation = {
      id: 1,
      deliverableId: 1,
      projectId: 1,
      teamId: null,
      studentId: 1,
      evaluatorId: 2,
      rubricId: 1,
      criteriaEvaluations: null,
      totalScore: 85,
      maxPossibleScore: 100,
      overallFeedback: "Buen trabajo",
      status: EvaluationStatus.COMPLETED,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    console.log(`   ✅ Evaluación creada: ID ${testEvaluation.id}`);
    console.log(`   ✅ Porcentaje: ${calculatePercentage(testEvaluation)}%`);
    console.log(`   ✅ Calificación: ${getLetterGrade(calculatePercentage(testEvaluation))}`);
    
    // Test 8: Report models
    console.log("\n8️⃣ Probando Report models...");
    
    const testReport: Report = {
      id: 1,
      title: "Reporte de Progreso",
      description: "Reporte semanal",
      projectId: 1,
      type: ReportType.PROGRESS,
      data: { progress: 75, tasks: 20 },
      generatedBy: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    console.log(`   ✅ Reporte creado: ${testReport.title}`);
    console.log(`   ✅ Icono del tipo: ${getReportTypeIcon(testReport.type)}`);
    
    // Test 9: Backlog Item models
    console.log("\n9️⃣ Probando Backlog Item models...");
    
    const testBacklogItem: BacklogItem = {
      id: 1,
      title: "Implementar dashboard",
      description: "Dashboard principal del sistema",
      projectId: 1,
      userStoryId: null,
      priority: 1,
      status: BacklogItemStatus.BACKLOG,
      estimatedEffort: 8,
      businessValue: 9,
      createdBy: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    console.log(`   ✅ Backlog Item creado: ${testBacklogItem.title}`);
    console.log(`   ✅ Score de prioridad: ${calculatePriorityScore(testBacklogItem)}`);
    
    // Test 10: Common utilities
    console.log("\n🔟 Probando utilidades comunes...");
    
    const testDate = new Date();
    console.log(`   ✅ Fecha formateada: ${formatDate(testDate)}`);
    console.log(`   ✅ Tiempo relativo: ${formatRelativeTime(new Date(Date.now() - 3600000))}`);
    console.log(`   ✅ Texto truncado: ${truncateText("Este es un texto muy largo", 20)}`);
    console.log(`   ✅ Slug: ${slugify("Título con Acentos y Espacios")}`);
    console.log(`   ✅ ID válido: ${isValidId(1)}`);
    console.log(`   ✅ Color de estado: ${getStatusColor("active")}`);
    
    console.log("\n🎉 ¡Todos los modelos funcionan correctamente!");
    
  } catch (error) {
    console.error("❌ Error durante las pruebas:", error);
    throw error;
  }
}

// Ejecutar las pruebas
if (import.meta.main) {
  testModels();
}
