#!/usr/bin/env -S deno run -A

/**
 * Script para verificar que el esquema de base de datos esté correctamente aplicado
 * Verifica que todas las tablas y columnas necesarias existan
 */

import { Client } from "https://deno.land/x/postgres@v0.17.0/mod.ts";

// Configuración de la base de datos
const DB_CONFIG = {
  user: Deno.env.get("DB_USER") || "postgres",
  password: Deno.env.get("DB_PASSWORD") || "postgres",
  database: Deno.env.get("DB_NAME") || "workflowsp",
  hostname: Deno.env.get("DB_HOST") || "localhost",
  port: parseInt(Deno.env.get("DB_PORT") || "5432"),
};

// Tablas esperadas y sus columnas principales
const EXPECTED_TABLES = {
  users: ["id", "name", "email", "password", "role", "created_at", "updated_at"],
  projects: ["id", "name", "description", "owner_id", "created_at", "updated_at"],
  teams: ["id", "name", "project_id", "created_at", "updated_at"],
  team_members: ["id", "team_id", "user_id", "role", "created_at", "updated_at"],
  sprints: ["id", "name", "goal", "description", "start_date", "end_date", "status", "project_id", "created_by", "created_at", "updated_at"],
  user_stories: ["id", "title", "description", "acceptance_criteria", "priority", "status", "points", "project_id", "sprint_id", "created_by", "created_at", "updated_at"],
  tasks: ["id", "title", "description", "status", "priority", "story_points", "estimated_hours", "spent_hours", "user_story_id", "sprint_id", "assignee_id", "assigned_to", "created_by", "created_at", "updated_at"],
  rubrics: ["id", "name", "description", "project_id", "is_template", "status", "criteria", "created_by", "created_at", "updated_at"],
  deliverables: ["id", "title", "description", "type", "status", "due_date", "submitted_at", "project_id", "user_story_id", "sprint_id", "submitted_by", "created_by", "created_at", "updated_at"],
  evaluations: ["id", "deliverable_id", "student_id", "evaluator_id", "rubric_id", "criteria_evaluations", "overall_feedback", "total_score", "max_possible_score", "percentage", "status", "type", "created_at", "updated_at"],
  backlog_items: ["id", "title", "description", "type", "status", "priority", "business_value", "effort", "risk_reduction", "time_criticality", "project_id", "user_story_id", "created_by", "created_at", "updated_at"],
  reports: ["id", "title", "type", "format", "data", "filters", "project_id", "sprint_id", "generated_by", "generated_at", "created_at", "updated_at"],
  deliverable_files: ["id", "filename", "original_name", "mime_type", "size", "path", "deliverable_id", "uploaded_by", "created_at"],
  project_members: ["id", "project_id", "user_id", "role", "permissions", "joined_at", "created_at", "updated_at"],
  comments: ["id", "content", "task_id", "user_id", "created_at", "updated_at"],
  migrations: ["id", "filename", "executed_at"],
};

async function getTableColumns(client: Client, tableName: string): Promise<string[]> {
  const result = await client.queryArray(`
    SELECT column_name 
    FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = $1
    ORDER BY ordinal_position
  `, [tableName]);
  
  return result.rows.map(row => row[0] as string);
}

async function checkTable(client: Client, tableName: string, expectedColumns: string[]): Promise<boolean> {
  try {
    const actualColumns = await getTableColumns(client, tableName);
    
    if (actualColumns.length === 0) {
      console.log(`❌ Tabla '${tableName}' no existe`);
      return false;
    }
    
    console.log(`✅ Tabla '${tableName}' existe`);
    
    // Verificar columnas
    const missingColumns = expectedColumns.filter(col => !actualColumns.includes(col));
    const extraColumns = actualColumns.filter(col => !expectedColumns.includes(col));
    
    if (missingColumns.length > 0) {
      console.log(`   ⚠️  Columnas faltantes: ${missingColumns.join(", ")}`);
    }
    
    if (extraColumns.length > 0) {
      console.log(`   ℹ️  Columnas adicionales: ${extraColumns.join(", ")}`);
    }
    
    if (missingColumns.length === 0) {
      console.log(`   ✅ Todas las columnas requeridas están presentes`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.log(`❌ Error verificando tabla '${tableName}':`, error.message);
    return false;
  }
}

async function checkForeignKeys(client: Client): Promise<void> {
  console.log("\n🔗 Verificando claves foráneas...");
  
  const result = await client.queryArray(`
    SELECT 
      tc.table_name, 
      kcu.column_name, 
      ccu.table_name AS foreign_table_name,
      ccu.column_name AS foreign_column_name 
    FROM 
      information_schema.table_constraints AS tc 
      JOIN information_schema.key_column_usage AS kcu
        ON tc.constraint_name = kcu.constraint_name
        AND tc.table_schema = kcu.table_schema
      JOIN information_schema.constraint_column_usage AS ccu
        ON ccu.constraint_name = tc.constraint_name
        AND ccu.table_schema = tc.table_schema
    WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_schema = 'public'
    ORDER BY tc.table_name, kcu.column_name
  `);
  
  if (result.rows.length > 0) {
    console.log(`✅ Encontradas ${result.rows.length} claves foráneas:`);
    result.rows.forEach(row => {
      const [table, column, foreignTable, foreignColumn] = row;
      console.log(`   ${table}.${column} → ${foreignTable}.${foreignColumn}`);
    });
  } else {
    console.log("⚠️  No se encontraron claves foráneas");
  }
}

async function checkIndexes(client: Client): Promise<void> {
  console.log("\n📊 Verificando índices...");
  
  const result = await client.queryArray(`
    SELECT 
      schemaname,
      tablename,
      indexname,
      indexdef
    FROM pg_indexes 
    WHERE schemaname = 'public'
    AND indexname NOT LIKE '%_pkey'
    ORDER BY tablename, indexname
  `);
  
  if (result.rows.length > 0) {
    console.log(`✅ Encontrados ${result.rows.length} índices personalizados:`);
    result.rows.forEach(row => {
      const [, tableName, indexName] = row;
      console.log(`   ${tableName}: ${indexName}`);
    });
  } else {
    console.log("ℹ️  No se encontraron índices personalizados");
  }
}

async function main() {
  console.log("🔍 Verificando esquema de base de datos...");
  console.log("=" .repeat(60));
  
  const client = new Client(DB_CONFIG);
  
  try {
    // Conectar a la base de datos
    console.log("🔌 Conectando a la base de datos...");
    await client.connect();
    console.log("✅ Conectado a la base de datos");
    
    // Verificar cada tabla
    console.log("\n📋 Verificando tablas...");
    let allTablesValid = true;
    
    for (const [tableName, expectedColumns] of Object.entries(EXPECTED_TABLES)) {
      const isValid = await checkTable(client, tableName, expectedColumns);
      if (!isValid) {
        allTablesValid = false;
      }
    }
    
    // Verificar claves foráneas
    await checkForeignKeys(client);
    
    // Verificar índices
    await checkIndexes(client);
    
    // Verificar triggers
    console.log("\n⚡ Verificando triggers...");
    const triggersResult = await client.queryArray(`
      SELECT 
        trigger_name,
        event_object_table,
        action_timing,
        event_manipulation
      FROM information_schema.triggers 
      WHERE trigger_schema = 'public'
      ORDER BY event_object_table, trigger_name
    `);
    
    if (triggersResult.rows.length > 0) {
      console.log(`✅ Encontrados ${triggersResult.rows.length} triggers:`);
      triggersResult.rows.forEach(row => {
        const [triggerName, tableName, timing, event] = row;
        console.log(`   ${tableName}: ${triggerName} (${timing} ${event})`);
      });
    } else {
      console.log("ℹ️  No se encontraron triggers");
    }
    
    // Resumen final
    console.log("\n" + "=" .repeat(60));
    
    if (allTablesValid) {
      console.log("🎉 ¡Esquema de base de datos verificado correctamente!");
      console.log("✅ Todas las tablas y columnas requeridas están presentes");
    } else {
      console.log("⚠️  El esquema tiene algunos problemas");
      console.log("💡 Ejecuta 'deno task migrate' para aplicar las migraciones");
    }
    
  } catch (error) {
    console.error("❌ Error durante la verificación:", error);
    Deno.exit(1);
  } finally {
    // Cerrar conexión
    await client.end();
    console.log("🔌 Conexión cerrada");
  }
}

// Ejecutar si es el archivo principal
if (import.meta.main) {
  await main();
}
