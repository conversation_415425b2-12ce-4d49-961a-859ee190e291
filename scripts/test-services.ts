#!/usr/bin/env -S deno run -A

/**
 * <PERSON><PERSON><PERSON> para probar todos los servicios de base de datos
 * Verifica que todas las funciones CRUD funcionen correctamente
 */

import {
  // User Story services
  createUserStory,
  getUserStoryById,
  getUserStoriesByProjectId,
  updateUserStory,
  deleteUserStory,
  
  // Rubric services
  createRubric,
  getRubricById,
  getRubricsByProjectId,
  updateRubric,
  deleteRubric,
  
  // Deliverable services
  createDeliverable,
  getDeliverableById,
  getDeliverablesByProjectId,
  updateDeliverable,
  deleteDeliverable,
  
  // Evaluation services
  createEvaluation,
  getEvaluationById,
  getEvaluationsByDeliverableId,
  updateEvaluation,
  deleteEvaluation,
  
  // Backlog Item services
  createBacklogItem,
  getBacklogItemById,
  getBacklogItemsByProjectId,
  updateBacklogItem,
  deleteBacklogItem,
  
  // Report services
  createReport,
  getReportById,
  getReportsByProjectId,
  updateReport,
  deleteReport,
  
  // Project Member services
  addProjectMember,
  getProjectMembers,
  updateProjectMember,
  removeProjectMember,
  
  // Advanced query services
  getUserStoriesWithDetails,
  getTasksWithDetails,
  getDeliverablesWithDetails,
  getEvaluationsWithDetails,
  getProjectMembersWithDetails,
  getSprintStats,
  getProjectStats,
  
  // Existing services
  getAllProjects,
  createProject,
  getAllUsers,
  createUser,
} from "../db/services.ts";

async function testUserStoryServices() {
  console.log("🔍 Probando servicios de User Stories...");
  
  try {
    // Obtener un proyecto existente o crear uno
    const projects = await getAllProjects();
    let projectId = projects[0]?.id;
    
    if (!projectId) {
      console.log("   📝 Creando proyecto de prueba...");
      const [newProject] = await createProject({
        name: "Proyecto de Prueba - User Stories",
        description: "Proyecto para probar servicios de User Stories",
        ownerId: 1,
      });
      projectId = newProject.id;
    }
    
    // Crear User Story
    console.log("   ➕ Creando User Story...");
    const [userStory] = await createUserStory({
      title: "Como usuario quiero hacer login",
      description: "Necesito poder autenticarme en el sistema",
      acceptanceCriteria: "- Formulario de login\n- Validación de credenciales\n- Redirección después del login",
      priority: "high",
      status: "backlog",
      points: 5,
      projectId: projectId,
    });
    
    console.log(`   ✅ User Story creada: ${userStory.title} (ID: ${userStory.id})`);
    
    // Obtener User Story por ID
    const [retrievedUserStory] = await getUserStoryById(userStory.id);
    console.log(`   ✅ User Story obtenida: ${retrievedUserStory.title}`);
    
    // Obtener User Stories por proyecto
    const projectUserStories = await getUserStoriesByProjectId(projectId);
    console.log(`   ✅ User Stories del proyecto: ${projectUserStories.length}`);
    
    // Actualizar User Story
    const [updatedUserStory] = await updateUserStory(userStory.id, {
      status: "in_progress",
      points: 8,
    });
    console.log(`   ✅ User Story actualizada: ${updatedUserStory.status}, ${updatedUserStory.points} puntos`);
    
    // Eliminar User Story
    await deleteUserStory(userStory.id);
    console.log(`   ✅ User Story eliminada`);
    
    return true;
  } catch (error) {
    console.error("   ❌ Error en servicios de User Stories:", error);
    return false;
  }
}

async function testRubricServices() {
  console.log("🔍 Probando servicios de Rubrics...");
  
  try {
    // Crear Rubric
    console.log("   ➕ Creando Rubric...");
    const [rubric] = await createRubric({
      name: "Rúbrica de Evaluación de Código",
      description: "Rúbrica para evaluar la calidad del código",
      isTemplate: true,
      status: "active",
      criteria: {
        "funcionalidad": {
          "levels": [
            { "name": "Excelente", "points": 4, "description": "Funciona perfectamente" },
            { "name": "Bueno", "points": 3, "description": "Funciona con errores menores" },
            { "name": "Regular", "points": 2, "description": "Funciona parcialmente" },
            { "name": "Deficiente", "points": 1, "description": "No funciona" }
          ]
        },
        "calidad": {
          "levels": [
            { "name": "Excelente", "points": 4, "description": "Código muy limpio" },
            { "name": "Bueno", "points": 3, "description": "Código limpio" },
            { "name": "Regular", "points": 2, "description": "Código aceptable" },
            { "name": "Deficiente", "points": 1, "description": "Código sucio" }
          ]
        }
      },
    });
    
    console.log(`   ✅ Rubric creada: ${rubric.name} (ID: ${rubric.id})`);
    
    // Obtener Rubric por ID
    const [retrievedRubric] = await getRubricById(rubric.id);
    console.log(`   ✅ Rubric obtenida: ${retrievedRubric.name}`);
    
    // Actualizar Rubric
    const [updatedRubric] = await updateRubric(rubric.id, {
      status: "archived",
    });
    console.log(`   ✅ Rubric actualizada: ${updatedRubric.status}`);
    
    // Eliminar Rubric
    await deleteRubric(rubric.id);
    console.log(`   ✅ Rubric eliminada`);
    
    return true;
  } catch (error) {
    console.error("   ❌ Error en servicios de Rubrics:", error);
    return false;
  }
}

async function testDeliverableServices() {
  console.log("🔍 Probando servicios de Deliverables...");
  
  try {
    // Obtener un proyecto existente
    const projects = await getAllProjects();
    const projectId = projects[0]?.id;
    
    if (!projectId) {
      console.log("   ⚠️  No hay proyectos disponibles para probar deliverables");
      return false;
    }
    
    // Crear Deliverable
    console.log("   ➕ Creando Deliverable...");
    const [deliverable] = await createDeliverable({
      title: "Documento de Arquitectura",
      description: "Documento que describe la arquitectura del sistema",
      type: "document",
      status: "pending",
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 días desde ahora
      projectId: projectId,
    });
    
    console.log(`   ✅ Deliverable creado: ${deliverable.title} (ID: ${deliverable.id})`);
    
    // Obtener Deliverable por ID
    const [retrievedDeliverable] = await getDeliverableById(deliverable.id);
    console.log(`   ✅ Deliverable obtenido: ${retrievedDeliverable.title}`);
    
    // Obtener Deliverables por proyecto
    const projectDeliverables = await getDeliverablesByProjectId(projectId);
    console.log(`   ✅ Deliverables del proyecto: ${projectDeliverables.length}`);
    
    // Actualizar Deliverable
    const [updatedDeliverable] = await updateDeliverable(deliverable.id, {
      status: "submitted",
      submittedAt: new Date(),
    });
    console.log(`   ✅ Deliverable actualizado: ${updatedDeliverable.status}`);
    
    // Eliminar Deliverable
    await deleteDeliverable(deliverable.id);
    console.log(`   ✅ Deliverable eliminado`);
    
    return true;
  } catch (error) {
    console.error("   ❌ Error en servicios de Deliverables:", error);
    return false;
  }
}

async function testAdvancedQueries() {
  console.log("🔍 Probando consultas avanzadas...");
  
  try {
    // Obtener un proyecto existente
    const projects = await getAllProjects();
    const projectId = projects[0]?.id;
    
    if (!projectId) {
      console.log("   ⚠️  No hay proyectos disponibles para probar consultas avanzadas");
      return false;
    }
    
    // Probar getUserStoriesWithDetails
    console.log("   🔗 Probando getUserStoriesWithDetails...");
    const userStoriesWithDetails = await getUserStoriesWithDetails(projectId);
    console.log(`   ✅ User Stories con detalles: ${userStoriesWithDetails.length}`);
    
    // Probar getTasksWithDetails
    console.log("   🔗 Probando getTasksWithDetails...");
    const tasksWithDetails = await getTasksWithDetails();
    console.log(`   ✅ Tasks con detalles: ${tasksWithDetails.length}`);
    
    // Probar getDeliverablesWithDetails
    console.log("   🔗 Probando getDeliverablesWithDetails...");
    const deliverablesWithDetails = await getDeliverablesWithDetails(projectId);
    console.log(`   ✅ Deliverables con detalles: ${deliverablesWithDetails.length}`);
    
    // Probar getProjectMembersWithDetails
    console.log("   🔗 Probando getProjectMembersWithDetails...");
    const membersWithDetails = await getProjectMembersWithDetails(projectId);
    console.log(`   ✅ Miembros con detalles: ${membersWithDetails.length}`);
    
    // Probar getProjectStats
    console.log("   📊 Probando getProjectStats...");
    const projectStats = await getProjectStats(projectId);
    console.log(`   ✅ Estadísticas del proyecto:`, projectStats);
    
    return true;
  } catch (error) {
    console.error("   ❌ Error en consultas avanzadas:", error);
    return false;
  }
}

async function main() {
  console.log("🧪 Iniciando pruebas de servicios de base de datos...");
  console.log("=" .repeat(60));
  
  const results = {
    userStories: false,
    rubrics: false,
    deliverables: false,
    advancedQueries: false,
  };
  
  try {
    // Probar servicios de User Stories
    results.userStories = await testUserStoryServices();
    console.log("");
    
    // Probar servicios de Rubrics
    results.rubrics = await testRubricServices();
    console.log("");
    
    // Probar servicios de Deliverables
    results.deliverables = await testDeliverableServices();
    console.log("");
    
    // Probar consultas avanzadas
    results.advancedQueries = await testAdvancedQueries();
    console.log("");
    
    // Resumen final
    console.log("=" .repeat(60));
    console.log("📊 Resumen de pruebas:");
    console.log(`   User Stories: ${results.userStories ? "✅ PASS" : "❌ FAIL"}`);
    console.log(`   Rubrics: ${results.rubrics ? "✅ PASS" : "❌ FAIL"}`);
    console.log(`   Deliverables: ${results.deliverables ? "✅ PASS" : "❌ FAIL"}`);
    console.log(`   Consultas Avanzadas: ${results.advancedQueries ? "✅ PASS" : "❌ FAIL"}`);
    
    const passedTests = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;
    
    console.log("");
    if (passedTests === totalTests) {
      console.log("🎉 ¡Todas las pruebas pasaron exitosamente!");
    } else {
      console.log(`⚠️  ${passedTests}/${totalTests} pruebas pasaron`);
    }
    
  } catch (error) {
    console.error("❌ Error durante las pruebas:", error);
    Deno.exit(1);
  }
}

// Ejecutar si es el archivo principal
if (import.meta.main) {
  await main();
}
