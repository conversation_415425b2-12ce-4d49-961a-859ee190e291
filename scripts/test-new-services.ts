import { 
  // User Stories
  createUserStory,
  getUserStoriesByProjectId,
  updateUserStory,
  deleteUserStory,
  
  // Rubrics
  createRubric,
  getRubricsByProjectId,
  getRubricTemplates,
  updateRubric,
  deleteRubric,
  duplicateRubric,
  
  // Deliverables
  createDeliverable,
  getDeliverablesByProjectId,
  updateDeliverable,
  deleteDeliverable,
  submitDeliverable,
  
  // Reports
  createReport,
  getReportsByProjectId,
  updateReport,
  deleteReport,
  
  // Backlog Items
  createBacklogItem,
  getBacklogItemsByProjectId,
  updateBacklogItem,
  deleteBacklogItem,
  
  // Project Members
  addProjectMember,
  getProjectMembers,
  removeProjectMember,
  
  // Utility functions
  getAllProjects,
  getAllUsers,
} from "../db/services.ts";
import { closePool } from "../db/db.ts";

/**
 * Script para probar los nuevos servicios de base de datos
 */
async function testNewServices() {
  console.log("🧪 Probando nuevos servicios de base de datos...");
  console.log("=" .repeat(60));
  
  try {
    // Obtener datos existentes
    const projects = await getAllProjects();
    const users = await getAllUsers();
    
    if (projects.length === 0 || users.length === 0) {
      console.log("❌ No hay proyectos o usuarios en la base de datos.");
      console.log("   Ejecuta primero: deno task init && deno task seed-projects");
      return;
    }
    
    const testProject = projects[0];
    const testUser = users[0];
    
    console.log(`📋 Usando proyecto: ${testProject.name} (ID: ${testProject.id})`);
    console.log(`👤 Usando usuario: ${testUser.name} (ID: ${testUser.id})`);
    console.log();
    
    // Test 1: User Stories
    console.log("1️⃣ Probando User Stories...");
    const [userStory] = await createUserStory({
      title: "Como usuario, quiero poder crear tareas",
      description: "Necesito poder crear tareas para organizar mi trabajo",
      acceptanceCriteria: "- Formulario de creación\n- Validación de campos\n- Guardado en BD",
      priority: "high",
      points: 5,
      projectId: testProject.id,
      status: "backlog",
      createdBy: testUser.id,
    });
    console.log(`   ✅ User Story creada: ${userStory.title}`);
    
    const projectUserStories = await getUserStoriesByProjectId(testProject.id);
    console.log(`   ✅ User Stories del proyecto: ${projectUserStories.length}`);
    
    // Test 2: Rubrics
    console.log("\n2️⃣ Probando Rubrics...");
    const [rubric] = await createRubric({
      name: "Rúbrica de Evaluación de Proyecto",
      description: "Rúbrica para evaluar proyectos de desarrollo",
      projectId: testProject.id,
      isTemplate: false,
      status: "draft",
      criteria: {
        criteria: [
          {
            id: "1",
            name: "Funcionalidad",
            description: "El proyecto cumple con los requisitos funcionales",
            maxPoints: 10,
            levels: [
              { id: "1", description: "Excelente", pointValue: 10 },
              { id: "2", description: "Bueno", pointValue: 7 },
              { id: "3", description: "Regular", pointValue: 4 },
              { id: "4", description: "Insuficiente", pointValue: 1 }
            ]
          }
        ]
      },
      createdBy: testUser.id,
    });
    console.log(`   ✅ Rúbrica creada: ${rubric.name}`);
    
    const projectRubrics = await getRubricsByProjectId(testProject.id);
    console.log(`   ✅ Rúbricas del proyecto: ${projectRubrics.length}`);
    
    // Test 3: Deliverables
    console.log("\n3️⃣ Probando Deliverables...");
    const [deliverable] = await createDeliverable({
      title: "Entregable 1 - Análisis de Requisitos",
      description: "Documento con el análisis completo de requisitos",
      projectId: testProject.id,
      userStoryId: userStory.id,
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 días
      status: "pending",
      createdBy: testUser.id,
    });
    console.log(`   ✅ Deliverable creado: ${deliverable.title}`);
    
    const projectDeliverables = await getDeliverablesByProjectId(testProject.id);
    console.log(`   ✅ Deliverables del proyecto: ${projectDeliverables.length}`);
    
    // Test 4: Reports
    console.log("\n4️⃣ Probando Reports...");
    const [report] = await createReport({
      title: "Reporte de Progreso Semanal",
      description: "Reporte automático del progreso del proyecto",
      projectId: testProject.id,
      type: "progress",
      data: {
        week: 1,
        completedTasks: 5,
        totalTasks: 10,
        progress: 50
      },
      generatedBy: testUser.id,
    });
    console.log(`   ✅ Reporte creado: ${report.title}`);
    
    const projectReports = await getReportsByProjectId(testProject.id);
    console.log(`   ✅ Reportes del proyecto: ${projectReports.length}`);
    
    // Test 5: Backlog Items
    console.log("\n5️⃣ Probando Backlog Items...");
    const [backlogItem] = await createBacklogItem({
      title: "Implementar autenticación de usuarios",
      description: "Sistema de login y registro de usuarios",
      projectId: testProject.id,
      userStoryId: userStory.id,
      priority: 1,
      status: "backlog",
      estimatedEffort: 8,
      businessValue: 9,
      createdBy: testUser.id,
    });
    console.log(`   ✅ Backlog Item creado: ${backlogItem.title}`);
    
    const projectBacklogItems = await getBacklogItemsByProjectId(testProject.id);
    console.log(`   ✅ Backlog Items del proyecto: ${projectBacklogItems.length}`);
    
    // Test 6: Project Members
    console.log("\n6️⃣ Probando Project Members...");
    const [projectMember] = await addProjectMember({
      projectId: testProject.id,
      userId: testUser.id,
      role: "team_member",
    });
    console.log(`   ✅ Miembro agregado al proyecto: ${testUser.name}`);
    
    const projectMembers = await getProjectMembers(testProject.id);
    console.log(`   ✅ Miembros del proyecto: ${projectMembers.length}`);
    
    // Test 7: Updates y Deletes
    console.log("\n7️⃣ Probando Updates y Deletes...");
    
    // Update User Story
    await updateUserStory(userStory.id, { status: "in_progress" });
    console.log(`   ✅ User Story actualizada`);
    
    // Update Deliverable
    await updateDeliverable(deliverable.id, { status: "in_progress" });
    console.log(`   ✅ Deliverable actualizado`);
    
    // Submit Deliverable
    await submitDeliverable(deliverable.id, testUser.id);
    console.log(`   ✅ Deliverable enviado`);
    
    // Cleanup - Delete test data
    console.log("\n🧹 Limpiando datos de prueba...");
    await removeProjectMember(projectMember.id);
    await deleteBacklogItem(backlogItem.id);
    await deleteReport(report.id);
    await deleteDeliverable(deliverable.id);
    await deleteRubric(rubric.id);
    await deleteUserStory(userStory.id);
    console.log(`   ✅ Datos de prueba eliminados`);
    
    console.log("\n🎉 ¡Todos los servicios funcionan correctamente!");
    
  } catch (error) {
    console.error("❌ Error durante las pruebas:", error);
  } finally {
    await closePool();
  }
}

// Ejecutar las pruebas
if (import.meta.main) {
  await testNewServices();
}
