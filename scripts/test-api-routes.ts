/**
 * Script para probar las nuevas rutas API
 */

const BASE_URL = "http://localhost:8000";

async function testApiRoutes() {
  console.log("🧪 Probando nuevas rutas API...");
  console.log("=" .repeat(60));
  
  try {
    // Test 1: Crear User Story
    console.log("1️⃣ Probando creación de User Story...");
    
    const userStoryData = {
      title: "Como usuario quiero hacer login",
      description: "Necesito poder autenticarme en el sistema",
      acceptanceCriteria: "- Formulario de login\n- Validación de credenciales\n- Redirección después del login",
      priority: "high",
      points: 5,
      projectId: 1,
      status: "backlog",
    };
    
    const createUserStoryResponse = await fetch(`${BASE_URL}/api/user-stories`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(userStoryData),
    });
    
    if (createUserStoryResponse.ok) {
      const userStoryResult = await createUserStoryResponse.json();
      console.log(`   ✅ User Story creada: ${userStoryResult.data.title}`);
      
      const userStoryId = userStoryResult.data.id;
      
      // Test 2: Obtener User Story por ID
      console.log("\n2️⃣ Probando obtención de User Story por ID...");
      
      const getUserStoryResponse = await fetch(`${BASE_URL}/api/user-stories/${userStoryId}`);
      
      if (getUserStoryResponse.ok) {
        const getUserStoryResult = await getUserStoryResponse.json();
        console.log(`   ✅ User Story obtenida: ${getUserStoryResult.data.title}`);
      } else {
        console.log(`   ❌ Error obteniendo User Story: ${getUserStoryResponse.status}`);
      }
      
      // Test 3: Actualizar User Story
      console.log("\n3️⃣ Probando actualización de User Story...");
      
      const updateUserStoryResponse = await fetch(`${BASE_URL}/api/user-stories/${userStoryId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status: "in_progress",
          points: 8,
        }),
      });
      
      if (updateUserStoryResponse.ok) {
        const updateUserStoryResult = await updateUserStoryResponse.json();
        console.log(`   ✅ User Story actualizada: ${updateUserStoryResult.data.status}`);
      } else {
        console.log(`   ❌ Error actualizando User Story: ${updateUserStoryResponse.status}`);
      }
      
      // Test 4: Crear Task
      console.log("\n4️⃣ Probando creación de Task...");
      
      const taskData = {
        title: "Implementar formulario de login",
        description: "Crear el componente de formulario de login con validación",
        userStoryId: userStoryId,
        status: "todo",
        priority: "high",
        estimatedHours: 8,
      };
      
      const createTaskResponse = await fetch(`${BASE_URL}/api/tasks`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(taskData),
      });
      
      if (createTaskResponse.ok) {
        const taskResult = await createTaskResponse.json();
        console.log(`   ✅ Task creada: ${taskResult.data.title}`);
        
        const taskId = taskResult.data.id;
        
        // Test 5: Obtener Task por ID
        console.log("\n5️⃣ Probando obtención de Task por ID...");
        
        const getTaskResponse = await fetch(`${BASE_URL}/api/tasks/${taskId}`);
        
        if (getTaskResponse.ok) {
          const getTaskResult = await getTaskResponse.json();
          console.log(`   ✅ Task obtenida: ${getTaskResult.data.title}`);
        } else {
          console.log(`   ❌ Error obteniendo Task: ${getTaskResponse.status}`);
        }
        
        // Test 6: Actualizar Task
        console.log("\n6️⃣ Probando actualización de Task...");
        
        const updateTaskResponse = await fetch(`${BASE_URL}/api/tasks/${taskId}`, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            status: "in_progress",
            spentHours: 2,
          }),
        });
        
        if (updateTaskResponse.ok) {
          const updateTaskResult = await updateTaskResponse.json();
          console.log(`   ✅ Task actualizada: ${updateTaskResult.data.status}`);
        } else {
          console.log(`   ❌ Error actualizando Task: ${updateTaskResponse.status}`);
        }
        
        // Test 7: Obtener Tasks por User Story
        console.log("\n7️⃣ Probando obtención de Tasks por User Story...");
        
        const getTasksByUserStoryResponse = await fetch(`${BASE_URL}/api/tasks?userStoryId=${userStoryId}`);
        
        if (getTasksByUserStoryResponse.ok) {
          const getTasksByUserStoryResult = await getTasksByUserStoryResponse.json();
          console.log(`   ✅ Tasks obtenidas: ${getTasksByUserStoryResult.data.length} task(s)`);
        } else {
          console.log(`   ❌ Error obteniendo Tasks: ${getTasksByUserStoryResponse.status}`);
        }
        
        // Cleanup: Eliminar Task
        console.log("\n🧹 Limpiando Task de prueba...");
        
        const deleteTaskResponse = await fetch(`${BASE_URL}/api/tasks/${taskId}`, {
          method: "DELETE",
        });
        
        if (deleteTaskResponse.ok) {
          console.log(`   ✅ Task eliminada`);
        } else {
          console.log(`   ❌ Error eliminando Task: ${deleteTaskResponse.status}`);
        }
      } else {
        console.log(`   ❌ Error creando Task: ${createTaskResponse.status}`);
      }
      
      // Test 8: Obtener User Stories por proyecto
      console.log("\n8️⃣ Probando obtención de User Stories por proyecto...");
      
      const getUserStoriesByProjectResponse = await fetch(`${BASE_URL}/api/user-stories?projectId=1`);
      
      if (getUserStoriesByProjectResponse.ok) {
        const getUserStoriesByProjectResult = await getUserStoriesByProjectResponse.json();
        console.log(`   ✅ User Stories obtenidas: ${getUserStoriesByProjectResult.data.length} historia(s)`);
      } else {
        console.log(`   ❌ Error obteniendo User Stories: ${getUserStoriesByProjectResponse.status}`);
      }
      
      // Cleanup: Eliminar User Story
      console.log("\n🧹 Limpiando User Story de prueba...");
      
      const deleteUserStoryResponse = await fetch(`${BASE_URL}/api/user-stories/${userStoryId}`, {
        method: "DELETE",
      });
      
      if (deleteUserStoryResponse.ok) {
        console.log(`   ✅ User Story eliminada`);
      } else {
        console.log(`   ❌ Error eliminando User Story: ${deleteUserStoryResponse.status}`);
      }
      
    } else {
      console.log(`   ❌ Error creando User Story: ${createUserStoryResponse.status}`);
      const errorData = await createUserStoryResponse.json();
      console.log(`   Error: ${errorData.error}`);
    }
    
    console.log("\n🎉 ¡Pruebas de API completadas!");
    
  } catch (error) {
    console.error("❌ Error durante las pruebas:", error);
  }
}

// Ejecutar las pruebas
if (import.meta.main) {
  await testApiRoutes();
}
