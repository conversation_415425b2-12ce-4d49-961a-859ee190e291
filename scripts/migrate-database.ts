#!/usr/bin/env -S deno run -A

/**
 * Script para aplicar migraciones de base de datos
 * Ejecuta las migraciones SQL en orden
 */

import { Client } from "https://deno.land/x/postgres@v0.17.0/mod.ts";

// Configuración de la base de datos
const DB_CONFIG = {
  user: Deno.env.get("DB_USER") || "postgres",
  password: Deno.env.get("DB_PASSWORD") || "postgres",
  database: Deno.env.get("DB_NAME") || "workflowsp",
  hostname: Deno.env.get("DB_HOST") || "localhost",
  port: parseInt(Deno.env.get("DB_PORT") || "5432"),
};

async function runMigration(client: Client, migrationFile: string) {
  console.log(`📄 Ejecutando migración: ${migrationFile}`);
  
  try {
    const migrationPath = `./db/migrations/${migrationFile}`;
    const migrationSQL = await Deno.readTextFile(migrationPath);
    
    // Ejecutar la migración
    await client.queryArray(migrationSQL);
    
    console.log(`✅ Migración completada: ${migrationFile}`);
  } catch (error) {
    console.error(`❌ Error en migración ${migrationFile}:`, error);
    throw error;
  }
}

async function createMigrationsTable(client: Client) {
  console.log("📋 Verificando tabla de migraciones...");
  
  const createMigrationsTableSQL = `
    CREATE TABLE IF NOT EXISTS migrations (
      id SERIAL PRIMARY KEY,
      filename VARCHAR(255) NOT NULL UNIQUE,
      executed_at TIMESTAMP DEFAULT NOW()
    );
  `;
  
  await client.queryArray(createMigrationsTableSQL);
  console.log("✅ Tabla de migraciones lista");
}

async function getMigrationsExecuted(client: Client): Promise<string[]> {
  const result = await client.queryArray(
    "SELECT filename FROM migrations ORDER BY executed_at"
  );
  
  return result.rows.map(row => row[0] as string);
}

async function markMigrationAsExecuted(client: Client, filename: string) {
  await client.queryArray(
    "INSERT INTO migrations (filename) VALUES ($1)",
    [filename]
  );
}

async function main() {
  console.log("🚀 Iniciando migraciones de base de datos...");
  console.log("=" .repeat(60));
  
  const client = new Client(DB_CONFIG);
  
  try {
    // Conectar a la base de datos
    console.log("🔌 Conectando a la base de datos...");
    await client.connect();
    console.log("✅ Conectado a la base de datos");
    
    // Crear tabla de migraciones si no existe
    await createMigrationsTable(client);
    
    // Obtener migraciones ya ejecutadas
    const executedMigrations = await getMigrationsExecuted(client);
    console.log(`📊 Migraciones ejecutadas: ${executedMigrations.length}`);
    
    // Lista de migraciones disponibles (en orden)
    const availableMigrations = [
      "001_initial_schema.sql",
      "002_add_component_tables.sql",
    ];
    
    // Ejecutar migraciones pendientes
    let migrationsExecuted = 0;
    
    for (const migration of availableMigrations) {
      if (!executedMigrations.includes(migration)) {
        try {
          // Verificar si el archivo existe
          const migrationPath = `./db/migrations/${migration}`;
          await Deno.stat(migrationPath);
          
          // Ejecutar migración
          await runMigration(client, migration);
          
          // Marcar como ejecutada
          await markMigrationAsExecuted(client, migration);
          
          migrationsExecuted++;
        } catch (error) {
          if (error instanceof Deno.errors.NotFound) {
            console.log(`⚠️  Archivo de migración no encontrado: ${migration}`);
            continue;
          }
          throw error;
        }
      } else {
        console.log(`⏭️  Migración ya ejecutada: ${migration}`);
      }
    }
    
    console.log("\n" + "=" .repeat(60));
    
    if (migrationsExecuted > 0) {
      console.log(`🎉 ¡Migraciones completadas! Se ejecutaron ${migrationsExecuted} migración(es)`);
    } else {
      console.log("✨ Base de datos ya está actualizada");
    }
    
    // Mostrar resumen de tablas
    console.log("\n📊 Verificando tablas en la base de datos...");
    const tablesResult = await client.queryArray(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);
    
    const tables = tablesResult.rows.map(row => row[0] as string);
    console.log(`✅ Tablas encontradas (${tables.length}):`);
    tables.forEach(table => console.log(`   - ${table}`));
    
  } catch (error) {
    console.error("❌ Error durante las migraciones:", error);
    Deno.exit(1);
  } finally {
    // Cerrar conexión
    await client.end();
    console.log("🔌 Conexión cerrada");
  }
}

// Ejecutar si es el archivo principal
if (import.meta.main) {
  await main();
}
