interface FormActionsProps {
  onCancel: () => void;
  isSubmitting: boolean;
  submitText?: string;
  submittingText?: string;
  cancelText?: string;
  className?: string;
}

export default function FormActions({
  onCancel,
  isSubmitting,
  submitText = "Guardar",
  submittingText = "Guardando...",
  cancelText = "Cancelar",
  className = "",
}: FormActionsProps) {
  return (
    <div className={`flex justify-end space-x-3 pt-4 ${className}`}>
      <button
        type="button"
        onClick={onCancel}
        disabled={isSubmitting}
        className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {cancelText}
      </button>
      <button
        type="submit"
        disabled={isSubmitting}
        className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isSubmitting ? submittingText : submitText}
      </button>
    </div>
  );
}
