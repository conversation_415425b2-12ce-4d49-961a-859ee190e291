import {
  pgTable,
  serial,
  text,
  varchar,
  timestamp,
  integer,
  boolean,
  decimal,
  json,
} from "drizzle-orm/pg-core";

// Users table
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  name: varchar("name", { length: 255 }).notNull(),
  email: varchar("email", { length: 255 }).notNull().unique(),
  password: varchar("password", { length: 255 }).notNull(),
  role: varchar("role", { length: 50 }).notNull().default("student"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Projects table
export const projects = pgTable("projects", {
  id: serial("id").primaryKey(),
  name: varchar("name", { length: 255 }).notNull(),
  description: text("description"),
  ownerId: integer("owner_id").notNull().references(() => users.id),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Teams table
export const teams = pgTable("teams", {
  id: serial("id").primaryKey(),
  name: varchar("name", { length: 255 }).notNull(),
  projectId: integer("project_id").notNull().references(() => projects.id),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Team members table
export const teamMembers = pgTable("team_members", {
  id: serial("id").primaryKey(),
  teamId: integer("team_id").notNull().references(() => teams.id),
  userId: integer("user_id").notNull().references(() => users.id),
  role: varchar("role", { length: 50 }).notNull().default("team_member"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Sprints table (updated)
export const sprints = pgTable("sprints", {
  id: serial("id").primaryKey(),
  name: varchar("name", { length: 255 }).notNull(),
  goal: text("goal"),
  description: text("description"),
  startDate: timestamp("start_date"),
  endDate: timestamp("end_date"),
  status: varchar("status", { length: 20 }).notNull().default("planned"),
  projectId: integer("project_id").notNull().references(() => projects.id),
  createdBy: integer("created_by").references(() => users.id),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Tasks table (updated)
export const tasks = pgTable("tasks", {
  id: serial("id").primaryKey(),
  title: varchar("title", { length: 255 }).notNull(),
  description: text("description"),
  status: varchar("status", { length: 20 }).notNull().default("todo"),
  priority: varchar("priority", { length: 20 }).notNull().default("medium"),
  storyPoints: integer("story_points"),
  estimatedHours: decimal("estimated_hours", { precision: 5, scale: 2 }),
  spentHours: decimal("spent_hours", { precision: 5, scale: 2 }).default("0"),
  userStoryId: integer("user_story_id").references(() => userStories.id),
  sprintId: integer("sprint_id").references(() => sprints.id),
  assigneeId: integer("assignee_id").references(() => users.id),
  assignedTo: varchar("assigned_to", { length: 255 }),
  createdBy: integer("created_by").references(() => users.id),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Comments table
export const comments = pgTable("comments", {
  id: serial("id").primaryKey(),
  content: text("content").notNull(),
  taskId: integer("task_id").notNull().references(() => tasks.id),
  userId: integer("user_id").notNull().references(() => users.id),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// User Stories table
export const userStories = pgTable("user_stories", {
  id: serial("id").primaryKey(),
  title: varchar("title", { length: 255 }).notNull(),
  description: text("description").notNull(),
  acceptanceCriteria: text("acceptance_criteria").notNull(),
  priority: varchar("priority", { length: 20 }).notNull().default("medium"),
  status: varchar("status", { length: 20 }).notNull().default("backlog"),
  points: integer("points"),
  projectId: integer("project_id").references(() => projects.id).notNull(),
  sprintId: integer("sprint_id").references(() => sprints.id),
  createdBy: integer("created_by").references(() => users.id),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Rubrics table
export const rubrics = pgTable("rubrics", {
  id: serial("id").primaryKey(),
  name: varchar("name", { length: 255 }).notNull(),
  description: text("description"),
  projectId: integer("project_id").references(() => projects.id),
  isTemplate: boolean("is_template").default(false),
  status: varchar("status", { length: 20 }).notNull().default("draft"),
  criteria: json("criteria").notNull(),
  createdBy: integer("created_by").references(() => users.id),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Deliverables table
export const deliverables = pgTable("deliverables", {
  id: serial("id").primaryKey(),
  title: varchar("title", { length: 255 }).notNull(),
  description: text("description"),
  type: varchar("type", { length: 50 }).notNull().default("document"),
  status: varchar("status", { length: 20 }).notNull().default("pending"),
  dueDate: timestamp("due_date"),
  submittedAt: timestamp("submitted_at"),
  projectId: integer("project_id").references(() => projects.id).notNull(),
  userStoryId: integer("user_story_id").references(() => userStories.id),
  sprintId: integer("sprint_id").references(() => sprints.id),
  submittedBy: integer("submitted_by").references(() => users.id),
  createdBy: integer("created_by").references(() => users.id),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Evaluations table (updated)
export const evaluations = pgTable("evaluations", {
  id: serial("id").primaryKey(),
  deliverableId: integer("deliverable_id").references(() => deliverables.id),
  studentId: integer("student_id").references(() => users.id).notNull(),
  evaluatorId: integer("evaluator_id").references(() => users.id).notNull(),
  rubricId: integer("rubric_id").references(() => rubrics.id).notNull(),
  criteriaEvaluations: json("criteria_evaluations").notNull(),
  overallFeedback: text("overall_feedback"),
  totalScore: decimal("total_score", { precision: 5, scale: 2 }).notNull(),
  maxPossibleScore: decimal("max_possible_score", { precision: 5, scale: 2 }).notNull(),
  percentage: decimal("percentage", { precision: 5, scale: 2 }),
  status: varchar("status", { length: 20 }).notNull().default("draft"),
  type: varchar("type", { length: 20 }).notNull().default("deliverable"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Backlog Items table
export const backlogItems = pgTable("backlog_items", {
  id: serial("id").primaryKey(),
  title: varchar("title", { length: 255 }).notNull(),
  description: text("description"),
  type: varchar("type", { length: 50 }).notNull().default("feature"),
  status: varchar("status", { length: 20 }).notNull().default("backlog"),
  priority: integer("priority").default(0),
  businessValue: integer("business_value").default(0),
  effort: integer("effort").default(0),
  riskReduction: integer("risk_reduction").default(0),
  timeCriticality: integer("time_criticality").default(0),
  projectId: integer("project_id").references(() => projects.id).notNull(),
  userStoryId: integer("user_story_id").references(() => userStories.id),
  createdBy: integer("created_by").references(() => users.id),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Reports table
export const reports = pgTable("reports", {
  id: serial("id").primaryKey(),
  title: varchar("title", { length: 255 }).notNull(),
  type: varchar("type", { length: 50 }).notNull(),
  format: varchar("format", { length: 20 }).notNull().default("json"),
  data: json("data").notNull(),
  filters: json("filters"),
  projectId: integer("project_id").references(() => projects.id).notNull(),
  sprintId: integer("sprint_id").references(() => sprints.id),
  generatedBy: integer("generated_by").references(() => users.id).notNull(),
  generatedAt: timestamp("generated_at").defaultNow(),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Deliverable Files table
export const deliverableFiles = pgTable("deliverable_files", {
  id: serial("id").primaryKey(),
  filename: varchar("filename", { length: 255 }).notNull(),
  originalName: varchar("original_name", { length: 255 }).notNull(),
  mimeType: varchar("mime_type", { length: 100 }).notNull(),
  size: integer("size").notNull(),
  path: varchar("path", { length: 500 }).notNull(),
  deliverableId: integer("deliverable_id").references(() => deliverables.id).notNull(),
  uploadedBy: integer("uploaded_by").references(() => users.id).notNull(),
  createdAt: timestamp("created_at").defaultNow(),
});

// Project Members table (for project access control)
export const projectMembers = pgTable("project_members", {
  id: serial("id").primaryKey(),
  projectId: integer("project_id").references(() => projects.id).notNull(),
  userId: integer("user_id").references(() => users.id).notNull(),
  role: varchar("role", { length: 50 }).notNull().default("member"),
  permissions: json("permissions"),
  joinedAt: timestamp("joined_at").defaultNow(),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});
