import { db } from "./db.ts";
import { eq, and, desc, asc } from "drizzle-orm";
import * as schema from "./schema.ts";

// User services
export async function createUser(userData: Omit<typeof schema.users.$inferInsert, "id" | "createdAt" | "updatedAt">) {
  return await db.insert(schema.users).values(userData).returning();
}

export async function getUserById(id: number) {
  return await db.select().from(schema.users).where(eq(schema.users.id, id)).limit(1);
}

export async function getUserByEmail(email: string) {
  return await db.select().from(schema.users).where(eq(schema.users.email, email));
}

export async function getAllUsers() {
  return await db.select().from(schema.users);
}

export async function updateUser(id: number, userData: Partial<Omit<typeof schema.users.$inferInsert, "id" | "createdAt" | "updatedAt">>) {
  return await db.update(schema.users)
    .set({
      ...userData,
      updatedAt: new Date()
    })
    .where(eq(schema.users.id, id))
    .returning();
}

export async function deleteUser(id: number) {
  return await db.delete(schema.users)
    .where(eq(schema.users.id, id))
    .returning();
}

export async function validateUserCredentials(email: string, password: string) {
  const users = await getUserByEmail(email);
  if (users.length === 0) {
    return null;
  }

  const user = users[0];
  // In a real application, you would hash the password and compare it with the stored hash
  // For now, we'll just compare the plain text passwords
  if (user.password !== password) {
    return null;
  }

  return user;
}

// Project services
export async function createProject(projectData: Omit<typeof schema.projects.$inferInsert, "id" | "createdAt" | "updatedAt">) {
  return await db.insert(schema.projects).values(projectData).returning();
}

export async function getProjectById(id: number) {
  return await db.select().from(schema.projects).where(eq(schema.projects.id, id)).limit(1);
}

export async function getAllProjects() {
  return await db.select().from(schema.projects);
}

export async function getProjectsByOwnerId(ownerId: number) {
  return await db.select().from(schema.projects).where(eq(schema.projects.ownerId, ownerId));
}

export async function updateProject(id: number, projectData: Partial<Omit<typeof schema.projects.$inferInsert, "id" | "createdAt" | "updatedAt">>) {
  return await db.update(schema.projects).set({
    ...projectData,
    updatedAt: new Date()
  }).where(eq(schema.projects.id, id)).returning();
}

export async function deleteProject(id: number) {
  return await db.delete(schema.projects).where(eq(schema.projects.id, id)).returning();
}

// Team services
export async function createTeam(teamData: Omit<typeof schema.teams.$inferInsert, "id" | "createdAt" | "updatedAt">) {
  return await db.insert(schema.teams).values(teamData).returning();
}

export async function getTeamById(id: number) {
  return await db.select().from(schema.teams).where(eq(schema.teams.id, id)).limit(1);
}

export async function getTeamsByProjectId(projectId: number) {
  return await db.select().from(schema.teams).where(eq(schema.teams.projectId, projectId));
}

// Team member services
export async function addTeamMember(teamMemberData: Omit<typeof schema.teamMembers.$inferInsert, "id" | "createdAt" | "updatedAt">) {
  return await db.insert(schema.teamMembers).values(teamMemberData).returning();
}

export async function getTeamMembersByTeamId(teamId: number) {
  return await db.select().from(schema.teamMembers).where(eq(schema.teamMembers.teamId, teamId));
}

// Sprint services
export async function createSprint(sprintData: Omit<typeof schema.sprints.$inferInsert, "id" | "createdAt" | "updatedAt">) {
  return await db.insert(schema.sprints).values(sprintData).returning();
}

export async function getSprintById(id: number) {
  return await db.select().from(schema.sprints).where(eq(schema.sprints.id, id)).limit(1);
}

export async function getSprintsByProjectId(projectId: number) {
  return await db.select().from(schema.sprints).where(eq(schema.sprints.projectId, projectId));
}

// Task services
export async function createTask(taskData: Omit<typeof schema.tasks.$inferInsert, "id" | "createdAt" | "updatedAt">) {
  return await db.insert(schema.tasks).values(taskData).returning();
}

export async function getTaskById(id: number) {
  return await db.select().from(schema.tasks).where(eq(schema.tasks.id, id)).limit(1);
}

export async function getTasksBySprintId(sprintId: number) {
  return await db.select().from(schema.tasks).where(eq(schema.tasks.sprintId, sprintId));
}

export async function getTasksByAssigneeId(assigneeId: number) {
  return await db.select().from(schema.tasks).where(eq(schema.tasks.assigneeId, assigneeId));
}

// Comment services
export async function createComment(commentData: Omit<typeof schema.comments.$inferInsert, "id" | "createdAt" | "updatedAt">) {
  return await db.insert(schema.comments).values(commentData).returning();
}

export async function getCommentsByTaskId(taskId: number) {
  return await db.select().from(schema.comments).where(eq(schema.comments.taskId, taskId));
}

// User Story services
export async function createUserStory(userStoryData: Omit<typeof schema.userStories.$inferInsert, "id" | "createdAt" | "updatedAt">) {
  return await db.insert(schema.userStories).values(userStoryData).returning();
}

export async function getUserStoryById(id: number) {
  return await db.select().from(schema.userStories).where(eq(schema.userStories.id, id)).limit(1);
}

export async function getUserStoriesByProjectId(projectId: number) {
  return await db.select().from(schema.userStories).where(eq(schema.userStories.projectId, projectId)).orderBy(desc(schema.userStories.createdAt));
}

export async function getUserStoriesBySprintId(sprintId: number) {
  return await db.select().from(schema.userStories).where(eq(schema.userStories.sprintId, sprintId)).orderBy(asc(schema.userStories.priority));
}

export async function updateUserStory(id: number, userStoryData: Partial<Omit<typeof schema.userStories.$inferInsert, "id" | "createdAt" | "updatedAt">>) {
  return await db.update(schema.userStories).set({
    ...userStoryData,
    updatedAt: new Date()
  }).where(eq(schema.userStories.id, id)).returning();
}

export async function deleteUserStory(id: number) {
  return await db.delete(schema.userStories).where(eq(schema.userStories.id, id)).returning();
}

// Updated Task services
export async function getTasksByUserStoryId(userStoryId: number) {
  return await db.select().from(schema.tasks).where(eq(schema.tasks.userStoryId, userStoryId));
}

export async function updateTask(id: number, taskData: Partial<Omit<typeof schema.tasks.$inferInsert, "id" | "createdAt" | "updatedAt">>) {
  return await db.update(schema.tasks).set({
    ...taskData,
    updatedAt: new Date()
  }).where(eq(schema.tasks.id, id)).returning();
}

export async function deleteTask(id: number) {
  return await db.delete(schema.tasks).where(eq(schema.tasks.id, id)).returning();
}

// Updated Sprint services
export async function updateSprint(id: number, sprintData: Partial<Omit<typeof schema.sprints.$inferInsert, "id" | "createdAt" | "updatedAt">>) {
  return await db.update(schema.sprints).set({
    ...sprintData,
    updatedAt: new Date()
  }).where(eq(schema.sprints.id, id)).returning();
}

export async function deleteSprint(id: number) {
  return await db.delete(schema.sprints).where(eq(schema.sprints.id, id)).returning();
}

// Rubric services
export async function createRubric(rubricData: Omit<typeof schema.rubrics.$inferInsert, "id" | "createdAt" | "updatedAt">) {
  return await db.insert(schema.rubrics).values(rubricData).returning();
}

export async function getRubricById(id: number) {
  return await db.select().from(schema.rubrics).where(eq(schema.rubrics.id, id)).limit(1);
}

export async function getRubricsByProjectId(projectId: number) {
  return await db.select().from(schema.rubrics).where(eq(schema.rubrics.projectId, projectId)).orderBy(desc(schema.rubrics.createdAt));
}

export async function getRubricTemplates() {
  return await db.select().from(schema.rubrics).where(eq(schema.rubrics.isTemplate, true)).orderBy(asc(schema.rubrics.name));
}

export async function updateRubric(id: number, rubricData: Partial<Omit<typeof schema.rubrics.$inferInsert, "id" | "createdAt" | "updatedAt">>) {
  return await db.update(schema.rubrics).set({
    ...rubricData,
    updatedAt: new Date()
  }).where(eq(schema.rubrics.id, id)).returning();
}

export async function deleteRubric(id: number) {
  return await db.delete(schema.rubrics).where(eq(schema.rubrics.id, id)).returning();
}

// Deliverable services
export async function createDeliverable(deliverableData: Omit<typeof schema.deliverables.$inferInsert, "id" | "createdAt" | "updatedAt">) {
  return await db.insert(schema.deliverables).values(deliverableData).returning();
}

export async function getDeliverableById(id: number) {
  return await db.select().from(schema.deliverables).where(eq(schema.deliverables.id, id)).limit(1);
}

export async function getDeliverablesByProjectId(projectId: number) {
  return await db.select().from(schema.deliverables).where(eq(schema.deliverables.projectId, projectId)).orderBy(desc(schema.deliverables.createdAt));
}

export async function getDeliverablesByUserStoryId(userStoryId: number) {
  return await db.select().from(schema.deliverables).where(eq(schema.deliverables.userStoryId, userStoryId));
}

export async function getDeliverablesBySprintId(sprintId: number) {
  return await db.select().from(schema.deliverables).where(eq(schema.deliverables.sprintId, sprintId));
}

export async function updateDeliverable(id: number, deliverableData: Partial<Omit<typeof schema.deliverables.$inferInsert, "id" | "createdAt" | "updatedAt">>) {
  return await db.update(schema.deliverables).set({
    ...deliverableData,
    updatedAt: new Date()
  }).where(eq(schema.deliverables.id, id)).returning();
}

export async function deleteDeliverable(id: number) {
  return await db.delete(schema.deliverables).where(eq(schema.deliverables.id, id)).returning();
}

// Updated Evaluation services
export async function createEvaluation(evaluationData: Omit<typeof schema.evaluations.$inferInsert, "id" | "createdAt" | "updatedAt">) {
  return await db.insert(schema.evaluations).values(evaluationData).returning();
}

export async function getEvaluationById(id: number) {
  return await db.select().from(schema.evaluations).where(eq(schema.evaluations.id, id)).limit(1);
}

export async function getEvaluationsByDeliverableId(deliverableId: number) {
  return await db.select().from(schema.evaluations).where(eq(schema.evaluations.deliverableId, deliverableId));
}

export async function getEvaluationsByStudentId(studentId: number) {
  return await db.select().from(schema.evaluations).where(eq(schema.evaluations.studentId, studentId)).orderBy(desc(schema.evaluations.createdAt));
}

export async function getEvaluationsByRubricId(rubricId: number) {
  return await db.select().from(schema.evaluations).where(eq(schema.evaluations.rubricId, rubricId));
}

export async function updateEvaluation(id: number, evaluationData: Partial<Omit<typeof schema.evaluations.$inferInsert, "id" | "createdAt" | "updatedAt">>) {
  return await db.update(schema.evaluations).set({
    ...evaluationData,
    updatedAt: new Date()
  }).where(eq(schema.evaluations.id, id)).returning();
}

export async function deleteEvaluation(id: number) {
  return await db.delete(schema.evaluations).where(eq(schema.evaluations.id, id)).returning();
}

// Backlog Item services
export async function createBacklogItem(backlogItemData: Omit<typeof schema.backlogItems.$inferInsert, "id" | "createdAt" | "updatedAt">) {
  return await db.insert(schema.backlogItems).values(backlogItemData).returning();
}

export async function getBacklogItemById(id: number) {
  return await db.select().from(schema.backlogItems).where(eq(schema.backlogItems.id, id)).limit(1);
}

export async function getBacklogItemsByProjectId(projectId: number) {
  return await db.select().from(schema.backlogItems).where(eq(schema.backlogItems.projectId, projectId)).orderBy(desc(schema.backlogItems.priority));
}

export async function getBacklogItemsByUserStoryId(userStoryId: number) {
  return await db.select().from(schema.backlogItems).where(eq(schema.backlogItems.userStoryId, userStoryId));
}

export async function updateBacklogItem(id: number, backlogItemData: Partial<Omit<typeof schema.backlogItems.$inferInsert, "id" | "createdAt" | "updatedAt">>) {
  return await db.update(schema.backlogItems).set({
    ...backlogItemData,
    updatedAt: new Date()
  }).where(eq(schema.backlogItems.id, id)).returning();
}

export async function deleteBacklogItem(id: number) {
  return await db.delete(schema.backlogItems).where(eq(schema.backlogItems.id, id)).returning();
}

// Report services
export async function createReport(reportData: Omit<typeof schema.reports.$inferInsert, "id" | "createdAt" | "updatedAt">) {
  return await db.insert(schema.reports).values(reportData).returning();
}

export async function getReportById(id: number) {
  return await db.select().from(schema.reports).where(eq(schema.reports.id, id)).limit(1);
}

export async function getReportsByProjectId(projectId: number) {
  return await db.select().from(schema.reports).where(eq(schema.reports.projectId, projectId)).orderBy(desc(schema.reports.generatedAt));
}

export async function getReportsBySprintId(sprintId: number) {
  return await db.select().from(schema.reports).where(eq(schema.reports.sprintId, sprintId));
}

export async function getReportsByType(type: string) {
  return await db.select().from(schema.reports).where(eq(schema.reports.type, type)).orderBy(desc(schema.reports.generatedAt));
}

export async function updateReport(id: number, reportData: Partial<Omit<typeof schema.reports.$inferInsert, "id" | "createdAt" | "updatedAt">>) {
  return await db.update(schema.reports).set({
    ...reportData,
    updatedAt: new Date()
  }).where(eq(schema.reports.id, id)).returning();
}

export async function deleteReport(id: number) {
  return await db.delete(schema.reports).where(eq(schema.reports.id, id)).returning();
}

// Deliverable File services
export async function createDeliverableFile(fileData: Omit<typeof schema.deliverableFiles.$inferInsert, "id" | "createdAt">) {
  return await db.insert(schema.deliverableFiles).values(fileData).returning();
}

export async function getDeliverableFileById(id: number) {
  return await db.select().from(schema.deliverableFiles).where(eq(schema.deliverableFiles.id, id)).limit(1);
}

export async function getDeliverableFilesByDeliverableId(deliverableId: number) {
  return await db.select().from(schema.deliverableFiles).where(eq(schema.deliverableFiles.deliverableId, deliverableId)).orderBy(desc(schema.deliverableFiles.createdAt));
}

export async function deleteDeliverableFile(id: number) {
  return await db.delete(schema.deliverableFiles).where(eq(schema.deliverableFiles.id, id)).returning();
}

// Project Member services
export async function addProjectMember(memberData: Omit<typeof schema.projectMembers.$inferInsert, "id" | "createdAt" | "updatedAt">) {
  return await db.insert(schema.projectMembers).values(memberData).returning();
}

export async function getProjectMemberById(id: number) {
  return await db.select().from(schema.projectMembers).where(eq(schema.projectMembers.id, id)).limit(1);
}

export async function getProjectMembers(projectId: number) {
  return await db.select().from(schema.projectMembers).where(eq(schema.projectMembers.projectId, projectId));
}

export async function getProjectMembersByUserId(userId: number) {
  return await db.select().from(schema.projectMembers).where(eq(schema.projectMembers.userId, userId));
}

export async function updateProjectMember(id: number, memberData: Partial<Omit<typeof schema.projectMembers.$inferInsert, "id" | "createdAt" | "updatedAt">>) {
  return await db.update(schema.projectMembers).set({
    ...memberData,
    updatedAt: new Date()
  }).where(eq(schema.projectMembers.id, id)).returning();
}

export async function removeProjectMember(id: number) {
  return await db.delete(schema.projectMembers).where(eq(schema.projectMembers.id, id)).returning();
}

export async function removeProjectMemberByUserAndProject(userId: number, projectId: number) {
  return await db.delete(schema.projectMembers)
    .where(and(eq(schema.projectMembers.userId, userId), eq(schema.projectMembers.projectId, projectId)))
    .returning();
}

// Advanced query functions for components

// Get User Stories with related data
export async function getUserStoriesWithDetails(projectId: number) {
  return await db.select({
    userStory: schema.userStories,
    project: schema.projects,
    sprint: schema.sprints,
    creator: schema.users,
  })
  .from(schema.userStories)
  .leftJoin(schema.projects, eq(schema.userStories.projectId, schema.projects.id))
  .leftJoin(schema.sprints, eq(schema.userStories.sprintId, schema.sprints.id))
  .leftJoin(schema.users, eq(schema.userStories.createdBy, schema.users.id))
  .where(eq(schema.userStories.projectId, projectId))
  .orderBy(desc(schema.userStories.createdAt));
}

// Get Tasks with related data
export async function getTasksWithDetails(sprintId?: number, userStoryId?: number) {
  const baseQuery = db.select({
    task: schema.tasks,
    userStory: schema.userStories,
    sprint: schema.sprints,
    assignee: schema.users,
  })
  .from(schema.tasks)
  .leftJoin(schema.userStories, eq(schema.tasks.userStoryId, schema.userStories.id))
  .leftJoin(schema.sprints, eq(schema.tasks.sprintId, schema.sprints.id))
  .leftJoin(schema.users, eq(schema.tasks.assigneeId, schema.users.id));

  if (sprintId) {
    return await baseQuery
      .where(eq(schema.tasks.sprintId, sprintId))
      .orderBy(desc(schema.tasks.createdAt));
  }

  if (userStoryId) {
    return await baseQuery
      .where(eq(schema.tasks.userStoryId, userStoryId))
      .orderBy(desc(schema.tasks.createdAt));
  }

  return await baseQuery.orderBy(desc(schema.tasks.createdAt));
}

// Get Deliverables with related data
export async function getDeliverablesWithDetails(projectId: number) {
  return await db.select({
    deliverable: schema.deliverables,
    project: schema.projects,
    userStory: schema.userStories,
    sprint: schema.sprints,
    submitter: schema.users,
  })
  .from(schema.deliverables)
  .leftJoin(schema.projects, eq(schema.deliverables.projectId, schema.projects.id))
  .leftJoin(schema.userStories, eq(schema.deliverables.userStoryId, schema.userStories.id))
  .leftJoin(schema.sprints, eq(schema.deliverables.sprintId, schema.sprints.id))
  .leftJoin(schema.users, eq(schema.deliverables.submittedBy, schema.users.id))
  .where(eq(schema.deliverables.projectId, projectId))
  .orderBy(desc(schema.deliverables.createdAt));
}

// Get Evaluations with related data
export async function getEvaluationsWithDetails(deliverableId?: number, studentId?: number) {
  const baseQuery = db.select({
    evaluation: schema.evaluations,
    deliverable: schema.deliverables,
    student: schema.users,
    rubric: schema.rubrics,
  })
  .from(schema.evaluations)
  .leftJoin(schema.deliverables, eq(schema.evaluations.deliverableId, schema.deliverables.id))
  .leftJoin(schema.users, eq(schema.evaluations.studentId, schema.users.id))
  .leftJoin(schema.rubrics, eq(schema.evaluations.rubricId, schema.rubrics.id));

  if (deliverableId) {
    return await baseQuery
      .where(eq(schema.evaluations.deliverableId, deliverableId))
      .orderBy(desc(schema.evaluations.createdAt));
  }

  if (studentId) {
    return await baseQuery
      .where(eq(schema.evaluations.studentId, studentId))
      .orderBy(desc(schema.evaluations.createdAt));
  }

  return await baseQuery.orderBy(desc(schema.evaluations.createdAt));
}

// Get Project Members with User details
export async function getProjectMembersWithDetails(projectId: number) {
  return await db.select({
    member: schema.projectMembers,
    user: schema.users,
    project: schema.projects,
  })
  .from(schema.projectMembers)
  .leftJoin(schema.users, eq(schema.projectMembers.userId, schema.users.id))
  .leftJoin(schema.projects, eq(schema.projectMembers.projectId, schema.projects.id))
  .where(eq(schema.projectMembers.projectId, projectId))
  .orderBy(asc(schema.users.name));
}

// Get Sprint statistics
export async function getSprintStats(sprintId: number) {
  const userStories = await getUserStoriesBySprintId(sprintId);
  const tasks = await getTasksBySprintId(sprintId);
  const deliverables = await getDeliverablesBySprintId(sprintId);

  return {
    userStoriesCount: userStories.length,
    tasksCount: tasks.length,
    deliverablesCount: deliverables.length,
    completedTasks: tasks.filter(task => task.status === 'done').length,
    totalStoryPoints: userStories.reduce((sum, story) => sum + (story.points || 0), 0),
    completedStoryPoints: userStories.filter(story => story.status === 'done').reduce((sum, story) => sum + (story.points || 0), 0),
  };
}

// Get Project statistics
export async function getProjectStats(projectId: number) {
  const userStories = await getUserStoriesByProjectId(projectId);
  const sprints = await getSprintsByProjectId(projectId);
  const deliverables = await getDeliverablesByProjectId(projectId);
  const members = await getProjectMembers(projectId);

  return {
    userStoriesCount: userStories.length,
    sprintsCount: sprints.length,
    deliverablesCount: deliverables.length,
    membersCount: members.length,
    completedUserStories: userStories.filter(story => story.status === 'done').length,
    activeSprintsCount: sprints.filter(sprint => sprint.status === 'active').length,
    totalStoryPoints: userStories.reduce((sum, story) => sum + (story.points || 0), 0),
  };
}
