import { db } from "./db.ts";
import { eq, and } from "drizzle-orm";
import * as schema from "./schema.ts";

// User services
export async function createUser(userData: Omit<typeof schema.users.$inferInsert, "id" | "createdAt" | "updatedAt">) {
  return await db.insert(schema.users).values(userData).returning();
}

export async function getUserById(id: number) {
  return await db.select().from(schema.users).where(eq(schema.users.id, id)).limit(1);
}

export async function getUserByEmail(email: string) {
  return await db.select().from(schema.users).where(eq(schema.users.email, email));
}

export async function getAllUsers() {
  return await db.select().from(schema.users);
}

export async function updateUser(id: number, userData: Partial<Omit<typeof schema.users.$inferInsert, "id" | "createdAt" | "updatedAt">>) {
  return await db.update(schema.users)
    .set({
      ...userData,
      updatedAt: new Date()
    })
    .where(eq(schema.users.id, id))
    .returning();
}

export async function deleteUser(id: number) {
  return await db.delete(schema.users)
    .where(eq(schema.users.id, id))
    .returning();
}

export async function validateUserCredentials(email: string, password: string) {
  const users = await getUserByEmail(email);
  if (users.length === 0) {
    return null;
  }

  const user = users[0];
  // In a real application, you would hash the password and compare it with the stored hash
  // For now, we'll just compare the plain text passwords
  if (user.password !== password) {
    return null;
  }

  return user;
}

// Project services
export async function createProject(projectData: Omit<typeof schema.projects.$inferInsert, "id" | "createdAt" | "updatedAt">) {
  return await db.insert(schema.projects).values(projectData).returning();
}

export async function getProjectById(id: number) {
  return await db.select().from(schema.projects).where(eq(schema.projects.id, id)).limit(1);
}

export async function getAllProjects() {
  return await db.select().from(schema.projects);
}

export async function getProjectsByOwnerId(ownerId: number) {
  return await db.select().from(schema.projects).where(eq(schema.projects.ownerId, ownerId));
}

export async function updateProject(id: number, projectData: Partial<Omit<typeof schema.projects.$inferInsert, "id" | "createdAt" | "updatedAt">>) {
  return await db.update(schema.projects).set({
    ...projectData,
    updatedAt: new Date()
  }).where(eq(schema.projects.id, id)).returning();
}

export async function deleteProject(id: number) {
  return await db.delete(schema.projects).where(eq(schema.projects.id, id)).returning();
}

// Team services
export async function createTeam(teamData: Omit<typeof schema.teams.$inferInsert, "id" | "createdAt" | "updatedAt">) {
  return await db.insert(schema.teams).values(teamData).returning();
}

export async function getTeamById(id: number) {
  return await db.select().from(schema.teams).where(eq(schema.teams.id, id)).limit(1);
}

export async function getTeamsByProjectId(projectId: number) {
  return await db.select().from(schema.teams).where(eq(schema.teams.projectId, projectId));
}

// Team member services
export async function addTeamMember(teamMemberData: Omit<typeof schema.teamMembers.$inferInsert, "id" | "createdAt" | "updatedAt">) {
  return await db.insert(schema.teamMembers).values(teamMemberData).returning();
}

export async function getTeamMembersByTeamId(teamId: number) {
  return await db.select().from(schema.teamMembers).where(eq(schema.teamMembers.teamId, teamId));
}

// Sprint services
export async function createSprint(sprintData: Omit<typeof schema.sprints.$inferInsert, "id" | "createdAt" | "updatedAt">) {
  return await db.insert(schema.sprints).values(sprintData).returning();
}

export async function getSprintById(id: number) {
  return await db.select().from(schema.sprints).where(eq(schema.sprints.id, id)).limit(1);
}

export async function getSprintsByProjectId(projectId: number) {
  return await db.select().from(schema.sprints).where(eq(schema.sprints.projectId, projectId));
}

// Task services
export async function createTask(taskData: Omit<typeof schema.tasks.$inferInsert, "id" | "createdAt" | "updatedAt">) {
  return await db.insert(schema.tasks).values(taskData).returning();
}

export async function getTaskById(id: number) {
  return await db.select().from(schema.tasks).where(eq(schema.tasks.id, id)).limit(1);
}

export async function getTasksBySprintId(sprintId: number) {
  return await db.select().from(schema.tasks).where(eq(schema.tasks.sprintId, sprintId));
}

export async function getTasksByAssigneeId(assigneeId: number) {
  return await db.select().from(schema.tasks).where(eq(schema.tasks.assigneeId, assigneeId));
}

// Comment services
export async function createComment(commentData: Omit<typeof schema.comments.$inferInsert, "id" | "createdAt" | "updatedAt">) {
  return await db.insert(schema.comments).values(commentData).returning();
}

export async function getCommentsByTaskId(taskId: number) {
  return await db.select().from(schema.comments).where(eq(schema.comments.taskId, taskId));
}

// Evaluation services
export async function createEvaluation(evaluationData: Omit<typeof schema.evaluations.$inferInsert, "id" | "createdAt" | "updatedAt">) {
  return await db.insert(schema.evaluations).values(evaluationData).returning();
}

export async function getEvaluationsByTeamId(teamId: number) {
  return await db.select().from(schema.evaluations).where(eq(schema.evaluations.teamId, teamId));
}

export async function getEvaluationsByProjectId(projectId: number) {
  return await db.select().from(schema.evaluations).where(eq(schema.evaluations.projectId, projectId));
}

export async function updateTask(id: number, taskData: Partial<Omit<typeof schema.tasks.$inferInsert, "id" | "createdAt" | "updatedAt">>) {
  return await db.update(schema.tasks)
    .set({
      ...taskData,
      updatedAt: new Date()
    })
    .where(eq(schema.tasks.id, id))
    .returning();
}

export async function deleteTask(id: number) {
  return await db.delete(schema.tasks)
    .where(eq(schema.tasks.id, id))
    .returning();
}

export async function getTasksByUserStoryId(userStoryId: number) {
  return await db.select().from(schema.tasks).where(eq(schema.tasks.userStoryId, userStoryId));
}

// User Stories services
export async function createUserStory(userStoryData: Omit<typeof schema.userStories.$inferInsert, "id" | "createdAt" | "updatedAt">) {
  return await db.insert(schema.userStories).values(userStoryData).returning();
}

export async function getUserStoryById(id: number) {
  return await db.select().from(schema.userStories).where(eq(schema.userStories.id, id)).limit(1);
}

export async function getUserStoriesByProjectId(projectId: number) {
  return await db.select().from(schema.userStories).where(eq(schema.userStories.projectId, projectId));
}

export async function getUserStoriesBySprintId(sprintId: number) {
  return await db.select().from(schema.userStories).where(eq(schema.userStories.sprintId, sprintId));
}

export async function updateUserStory(id: number, userStoryData: Partial<Omit<typeof schema.userStories.$inferInsert, "id" | "createdAt" | "updatedAt">>) {
  return await db.update(schema.userStories)
    .set({
      ...userStoryData,
      updatedAt: new Date()
    })
    .where(eq(schema.userStories.id, id))
    .returning();
}

export async function deleteUserStory(id: number) {
  return await db.delete(schema.userStories)
    .where(eq(schema.userStories.id, id))
    .returning();
}

// Rubrics services
export async function createRubric(rubricData: Omit<typeof schema.rubrics.$inferInsert, "id" | "createdAt" | "updatedAt">) {
  return await db.insert(schema.rubrics).values(rubricData).returning();
}

export async function getRubricById(id: number) {
  return await db.select().from(schema.rubrics).where(eq(schema.rubrics.id, id)).limit(1);
}

export async function getRubricsByProjectId(projectId: number) {
  return await db.select().from(schema.rubrics).where(eq(schema.rubrics.projectId, projectId));
}

export async function getRubricTemplates() {
  return await db.select().from(schema.rubrics).where(eq(schema.rubrics.isTemplate, true));
}

export async function updateRubric(id: number, rubricData: Partial<Omit<typeof schema.rubrics.$inferInsert, "id" | "createdAt" | "updatedAt">>) {
  return await db.update(schema.rubrics)
    .set({
      ...rubricData,
      updatedAt: new Date()
    })
    .where(eq(schema.rubrics.id, id))
    .returning();
}

export async function deleteRubric(id: number) {
  return await db.delete(schema.rubrics)
    .where(eq(schema.rubrics.id, id))
    .returning();
}

export async function duplicateRubric(id: number, newData: { name: string; projectId?: number; isTemplate?: boolean }) {
  const [originalRubric] = await getRubricById(id);
  if (!originalRubric) {
    throw new Error("Rubric not found");
  }

  const duplicatedRubric = {
    name: newData.name,
    description: originalRubric.description,
    projectId: newData.projectId || originalRubric.projectId,
    isTemplate: newData.isTemplate ?? originalRubric.isTemplate,
    status: "draft" as const,
    criteria: originalRubric.criteria,
    createdBy: originalRubric.createdBy,
  };

  return await createRubric(duplicatedRubric);
}

// Deliverables services
export async function createDeliverable(deliverableData: Omit<typeof schema.deliverables.$inferInsert, "id" | "createdAt" | "updatedAt">) {
  return await db.insert(schema.deliverables).values(deliverableData).returning();
}

export async function getDeliverableById(id: number) {
  return await db.select().from(schema.deliverables).where(eq(schema.deliverables.id, id)).limit(1);
}

export async function getDeliverablesByProjectId(projectId: number) {
  return await db.select().from(schema.deliverables).where(eq(schema.deliverables.projectId, projectId));
}

export async function getDeliverablesBySprintId(sprintId: number) {
  return await db.select().from(schema.deliverables).where(eq(schema.deliverables.sprintId, sprintId));
}

export async function getDeliverablesByUserStoryId(userStoryId: number) {
  return await db.select().from(schema.deliverables).where(eq(schema.deliverables.userStoryId, userStoryId));
}

export async function updateDeliverable(id: number, deliverableData: Partial<Omit<typeof schema.deliverables.$inferInsert, "id" | "createdAt" | "updatedAt">>) {
  return await db.update(schema.deliverables)
    .set({
      ...deliverableData,
      updatedAt: new Date()
    })
    .where(eq(schema.deliverables.id, id))
    .returning();
}

export async function deleteDeliverable(id: number) {
  return await db.delete(schema.deliverables)
    .where(eq(schema.deliverables.id, id))
    .returning();
}

export async function submitDeliverable(id: number, submittedBy: number) {
  return await updateDeliverable(id, {
    status: "submitted",
    submittedAt: new Date(),
    submittedBy: submittedBy
  });
}

// Extended Evaluation services
export async function updateEvaluation(id: number, evaluationData: Partial<Omit<typeof schema.evaluations.$inferInsert, "id" | "createdAt" | "updatedAt">>) {
  return await db.update(schema.evaluations)
    .set({
      ...evaluationData,
      updatedAt: new Date()
    })
    .where(eq(schema.evaluations.id, id))
    .returning();
}

export async function deleteEvaluation(id: number) {
  return await db.delete(schema.evaluations)
    .where(eq(schema.evaluations.id, id))
    .returning();
}

export async function getEvaluationById(id: number) {
  return await db.select().from(schema.evaluations).where(eq(schema.evaluations.id, id)).limit(1);
}

export async function getEvaluationsByDeliverableId(deliverableId: number) {
  return await db.select().from(schema.evaluations).where(eq(schema.evaluations.deliverableId, deliverableId));
}

export async function getEvaluationsByStudentId(studentId: number) {
  return await db.select().from(schema.evaluations).where(eq(schema.evaluations.studentId, studentId));
}

export async function getEvaluationsByRubricId(rubricId: number) {
  return await db.select().from(schema.evaluations).where(eq(schema.evaluations.rubricId, rubricId));
}

// Reports services
export async function createReport(reportData: Omit<typeof schema.reports.$inferInsert, "id" | "createdAt" | "updatedAt">) {
  return await db.insert(schema.reports).values(reportData).returning();
}

export async function getReportById(id: number) {
  return await db.select().from(schema.reports).where(eq(schema.reports.id, id)).limit(1);
}

export async function getReportsByProjectId(projectId: number) {
  return await db.select().from(schema.reports).where(eq(schema.reports.projectId, projectId));
}

export async function updateReport(id: number, reportData: Partial<Omit<typeof schema.reports.$inferInsert, "id" | "createdAt" | "updatedAt">>) {
  return await db.update(schema.reports)
    .set({
      ...reportData,
      updatedAt: new Date()
    })
    .where(eq(schema.reports.id, id))
    .returning();
}

export async function deleteReport(id: number) {
  return await db.delete(schema.reports)
    .where(eq(schema.reports.id, id))
    .returning();
}

// Backlog Items services
export async function createBacklogItem(backlogItemData: Omit<typeof schema.backlogItems.$inferInsert, "id" | "createdAt" | "updatedAt">) {
  return await db.insert(schema.backlogItems).values(backlogItemData).returning();
}

export async function getBacklogItemById(id: number) {
  return await db.select().from(schema.backlogItems).where(eq(schema.backlogItems.id, id)).limit(1);
}

export async function getBacklogItemsByProjectId(projectId: number) {
  return await db.select().from(schema.backlogItems).where(eq(schema.backlogItems.projectId, projectId));
}

export async function getBacklogItemsByUserStoryId(userStoryId: number) {
  return await db.select().from(schema.backlogItems).where(eq(schema.backlogItems.userStoryId, userStoryId));
}

export async function updateBacklogItem(id: number, backlogItemData: Partial<Omit<typeof schema.backlogItems.$inferInsert, "id" | "createdAt" | "updatedAt">>) {
  return await db.update(schema.backlogItems)
    .set({
      ...backlogItemData,
      updatedAt: new Date()
    })
    .where(eq(schema.backlogItems.id, id))
    .returning();
}

export async function deleteBacklogItem(id: number) {
  return await db.delete(schema.backlogItems)
    .where(eq(schema.backlogItems.id, id))
    .returning();
}

// Project Members services
export async function addProjectMember(projectMemberData: Omit<typeof schema.projectMembers.$inferInsert, "id" | "createdAt" | "updatedAt">) {
  return await db.insert(schema.projectMembers).values(projectMemberData).returning();
}

export async function getProjectMembersByProjectId(projectId: number) {
  return await db.select().from(schema.projectMembers).where(eq(schema.projectMembers.projectId, projectId));
}

export async function getProjectMembersByUserId(userId: number) {
  return await db.select().from(schema.projectMembers).where(eq(schema.projectMembers.userId, userId));
}

export async function updateProjectMember(id: number, projectMemberData: Partial<Omit<typeof schema.projectMembers.$inferInsert, "id" | "createdAt" | "updatedAt">>) {
  return await db.update(schema.projectMembers)
    .set({
      ...projectMemberData,
      updatedAt: new Date()
    })
    .where(eq(schema.projectMembers.id, id))
    .returning();
}

export async function removeProjectMember(id: number) {
  return await db.delete(schema.projectMembers)
    .where(eq(schema.projectMembers.id, id))
    .returning();
}

export async function removeProjectMemberByUserAndProject(userId: number, projectId: number) {
  return await db.delete(schema.projectMembers)
    .where(and(
      eq(schema.projectMembers.userId, userId),
      eq(schema.projectMembers.projectId, projectId)
    ))
    .returning();
}

// Extended Sprint services
export async function updateSprint(id: number, sprintData: Partial<Omit<typeof schema.sprints.$inferInsert, "id" | "createdAt" | "updatedAt">>) {
  return await db.update(schema.sprints)
    .set({
      ...sprintData,
      updatedAt: new Date()
    })
    .where(eq(schema.sprints.id, id))
    .returning();
}

export async function deleteSprint(id: number) {
  return await db.delete(schema.sprints)
    .where(eq(schema.sprints.id, id))
    .returning();
}

// Utility functions for complex queries
export async function getProjectMembers(projectId: number) {
  return await db
    .select({
      id: schema.projectMembers.id,
      userId: schema.projectMembers.userId,
      projectId: schema.projectMembers.projectId,
      role: schema.projectMembers.role,
      joinedAt: schema.projectMembers.joinedAt,
      username: schema.users.username,
      firstName: schema.users.firstName,
      lastName: schema.users.lastName,
      email: schema.users.email,
      name: schema.users.name,
    })
    .from(schema.projectMembers)
    .leftJoin(schema.users, eq(schema.projectMembers.userId, schema.users.id))
    .where(eq(schema.projectMembers.projectId, projectId));
}

export async function getUserStoryTasks(userStoryId: string) {
  const userStoryIdNum = Number.parseInt(userStoryId);
  return await db.select().from(schema.tasks).where(eq(schema.tasks.userStoryId, userStoryIdNum));
}

export async function getUserByIdString(userId: string) {
  const userIdNum = Number.parseInt(userId);
  const users = await db.select().from(schema.users).where(eq(schema.users.id, userIdNum)).limit(1);
  return users[0] || null;
}
