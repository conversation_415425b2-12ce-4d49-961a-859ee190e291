-- Migration: Add tables for component support
-- This migration adds all the tables needed to support the components from the DenoKV project

-- Add new columns to existing sprints table
ALTER TABLE sprints 
ADD COLUMN IF NOT EXISTS goal TEXT,
ALTER COLUMN start_date DROP NOT NULL,
ALTER COLUMN end_date DROP NOT NULL,
ALTER COLUMN status TYPE VARCHAR(20),
ADD COLUMN IF NOT EXISTS created_by INTEGER REFERENCES users(id);

-- Add new columns to existing tasks table
ALTER TABLE tasks 
ADD COLUMN IF NOT EXISTS estimated_hours DECIMAL(5,2),
ADD COLUMN IF NOT EXISTS spent_hours DECIMAL(5,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS user_story_id INTEGER,
ADD COLUMN IF NOT EXISTS assigned_to VARCHAR(255),
ADD COLUMN IF NOT EXISTS created_by INTEGER REFERENCES users(id),
ALTER COLUMN status TYPE VARCHAR(20),
ALTER COLUMN priority TYPE VARCHAR(20);

-- Create user_stories table
CREATE TABLE IF NOT EXISTS user_stories (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    acceptance_criteria TEXT NOT NULL,
    priority VARCHAR(20) NOT NULL DEFAULT 'medium',
    status VARCHAR(20) NOT NULL DEFAULT 'backlog',
    points INTEGER,
    project_id INTEGER NOT NULL REFERENCES projects(id),
    sprint_id INTEGER REFERENCES sprints(id),
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Add foreign key constraint for tasks.user_story_id
ALTER TABLE tasks 
ADD CONSTRAINT fk_tasks_user_story 
FOREIGN KEY (user_story_id) REFERENCES user_stories(id);

-- Create rubrics table
CREATE TABLE IF NOT EXISTS rubrics (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    project_id INTEGER REFERENCES projects(id),
    is_template BOOLEAN DEFAULT FALSE,
    status VARCHAR(20) NOT NULL DEFAULT 'draft',
    criteria JSON NOT NULL,
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create deliverables table
CREATE TABLE IF NOT EXISTS deliverables (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL DEFAULT 'document',
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    due_date TIMESTAMP,
    submitted_at TIMESTAMP,
    project_id INTEGER NOT NULL REFERENCES projects(id),
    user_story_id INTEGER REFERENCES user_stories(id),
    sprint_id INTEGER REFERENCES sprints(id),
    submitted_by INTEGER REFERENCES users(id),
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Update evaluations table to match new schema
DROP TABLE IF EXISTS evaluations;
CREATE TABLE evaluations (
    id SERIAL PRIMARY KEY,
    deliverable_id INTEGER REFERENCES deliverables(id),
    student_id INTEGER NOT NULL REFERENCES users(id),
    evaluator_id INTEGER NOT NULL REFERENCES users(id),
    rubric_id INTEGER NOT NULL REFERENCES rubrics(id),
    criteria_evaluations JSON NOT NULL,
    overall_feedback TEXT,
    total_score DECIMAL(5,2) NOT NULL,
    max_possible_score DECIMAL(5,2) NOT NULL,
    percentage DECIMAL(5,2),
    status VARCHAR(20) NOT NULL DEFAULT 'draft',
    type VARCHAR(20) NOT NULL DEFAULT 'deliverable',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create backlog_items table
CREATE TABLE IF NOT EXISTS backlog_items (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL DEFAULT 'feature',
    status VARCHAR(20) NOT NULL DEFAULT 'backlog',
    priority INTEGER DEFAULT 0,
    business_value INTEGER DEFAULT 0,
    effort INTEGER DEFAULT 0,
    risk_reduction INTEGER DEFAULT 0,
    time_criticality INTEGER DEFAULT 0,
    project_id INTEGER NOT NULL REFERENCES projects(id),
    user_story_id INTEGER REFERENCES user_stories(id),
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create reports table
CREATE TABLE IF NOT EXISTS reports (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL,
    format VARCHAR(20) NOT NULL DEFAULT 'json',
    data JSON NOT NULL,
    filters JSON,
    project_id INTEGER NOT NULL REFERENCES projects(id),
    sprint_id INTEGER REFERENCES sprints(id),
    generated_by INTEGER NOT NULL REFERENCES users(id),
    generated_at TIMESTAMP DEFAULT NOW(),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create deliverable_files table
CREATE TABLE IF NOT EXISTS deliverable_files (
    id SERIAL PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    size INTEGER NOT NULL,
    path VARCHAR(500) NOT NULL,
    deliverable_id INTEGER NOT NULL REFERENCES deliverables(id),
    uploaded_by INTEGER NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Create project_members table (if not exists)
CREATE TABLE IF NOT EXISTS project_members (
    id SERIAL PRIMARY KEY,
    project_id INTEGER NOT NULL REFERENCES projects(id),
    user_id INTEGER NOT NULL REFERENCES users(id),
    role VARCHAR(50) NOT NULL DEFAULT 'member',
    permissions JSON,
    joined_at TIMESTAMP DEFAULT NOW(),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(project_id, user_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_stories_project_id ON user_stories(project_id);
CREATE INDEX IF NOT EXISTS idx_user_stories_sprint_id ON user_stories(sprint_id);
CREATE INDEX IF NOT EXISTS idx_user_stories_status ON user_stories(status);
CREATE INDEX IF NOT EXISTS idx_user_stories_priority ON user_stories(priority);

CREATE INDEX IF NOT EXISTS idx_tasks_user_story_id ON tasks(user_story_id);
CREATE INDEX IF NOT EXISTS idx_tasks_sprint_id ON tasks(sprint_id);
CREATE INDEX IF NOT EXISTS idx_tasks_assignee_id ON tasks(assignee_id);
CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status);

CREATE INDEX IF NOT EXISTS idx_deliverables_project_id ON deliverables(project_id);
CREATE INDEX IF NOT EXISTS idx_deliverables_user_story_id ON deliverables(user_story_id);
CREATE INDEX IF NOT EXISTS idx_deliverables_sprint_id ON deliverables(sprint_id);
CREATE INDEX IF NOT EXISTS idx_deliverables_status ON deliverables(status);

CREATE INDEX IF NOT EXISTS idx_evaluations_deliverable_id ON evaluations(deliverable_id);
CREATE INDEX IF NOT EXISTS idx_evaluations_student_id ON evaluations(student_id);
CREATE INDEX IF NOT EXISTS idx_evaluations_rubric_id ON evaluations(rubric_id);

CREATE INDEX IF NOT EXISTS idx_backlog_items_project_id ON backlog_items(project_id);
CREATE INDEX IF NOT EXISTS idx_backlog_items_user_story_id ON backlog_items(user_story_id);
CREATE INDEX IF NOT EXISTS idx_backlog_items_priority ON backlog_items(priority);

CREATE INDEX IF NOT EXISTS idx_reports_project_id ON reports(project_id);
CREATE INDEX IF NOT EXISTS idx_reports_sprint_id ON reports(sprint_id);
CREATE INDEX IF NOT EXISTS idx_reports_type ON reports(type);

CREATE INDEX IF NOT EXISTS idx_project_members_project_id ON project_members(project_id);
CREATE INDEX IF NOT EXISTS idx_project_members_user_id ON project_members(user_id);

-- Add triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to all tables with updated_at
CREATE TRIGGER update_user_stories_updated_at BEFORE UPDATE ON user_stories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tasks_updated_at BEFORE UPDATE ON tasks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_sprints_updated_at BEFORE UPDATE ON sprints FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_rubrics_updated_at BEFORE UPDATE ON rubrics FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_deliverables_updated_at BEFORE UPDATE ON deliverables FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_evaluations_updated_at BEFORE UPDATE ON evaluations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_backlog_items_updated_at BEFORE UPDATE ON backlog_items FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_reports_updated_at BEFORE UPDATE ON reports FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_project_members_updated_at BEFORE UPDATE ON project_members FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
